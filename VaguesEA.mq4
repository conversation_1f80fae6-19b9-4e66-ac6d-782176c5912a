//+------------------------------------------------------------------+
//|                                                      VaguesEA.mq4|
//|                        إكسبرت مبني على مؤشر Vagues               |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Alsayaf EA"
#property link      ""
#property version   "1.00"
#property strict

//--- Input parameters
input group "=== إعدادات التداول ==="
input double LotSize = 0.1;           // حجم اللوت
input int MagicNumber = 12345;        // الرقم السحري
input int Slippage = 3;               // الانزلاق
input int StopLoss = 100;             // وقف الخسارة بالنقاط
input int TakeProfit = 200;           // جني الأرباح بالنقاط
input bool UseStopLoss = true;        // استخدام وقف الخسارة
input bool UseTakeProfit = true;      // استخدام جني الأرباح

input group "=== إعدادات مؤشر Vagues ==="
input int History = 1000;             // عدد الشموع للتحليل
input int Begin_draw_down = 5;        // بداية الرسم للأسفل
input int Begin_draw_up = 5;          // بداية الرسم للأعلى
input int Sektor_bar = 13;            // شموع القطاع
input int f_period_draw = 1;          // فترة الرسم
input int clear_opposite = 4;         // مسح المقابل
input int Wawe_bar = 21;              // شموع الموجة
input bool f_draw_wawe_fibo = true;   // رسم فيبوناتشي الموجة
input bool f_draw_target_level = true; // رسم مستوى الهدف
input int DM_sdvig = 7;               // إزاحة DM
input int DM_xvost = 10;              // ذيل DM
input bool f_draw_mini_trend = false; // رسم الاتجاه المصغر

input group "=== إعدادات فيبوناتشي ==="
input double FiboLevel = 0.618;       // مستوى فيبوناتشي للدخول (61.8%)
input double FiboTolerance = 5.0;     // التسامح في النقاط لملامسة المستوى
input color RedFiboColor = clrRed;    // لون فيبوناتشي الأحمر (إشارة شراء)
input color GreenFiboColor = clrLime; // لون فيبوناتشي الأخضر (إشارة بيع)
input bool CloseOppositeSignals = true; // إغلاق الصفقات المعاكسة عند الإشارة الجديدة
input bool OneTradePerSignal = true;  // صفقة واحدة فقط لكل إشارة
input bool EnableAlerts = true;       // تفعيل التنبيهات
input bool ShowDebugInfo = true;      // عرض معلومات التشخيص

//--- Global variables
bool lastBuySignal = false;
bool lastSellSignal = false;
datetime lastTradeTime = 0;
int minTimeBetweenTrades = 60; // دقيقة واحدة بين الصفقات

//--- Trading statistics
int totalBuyTrades = 0;
int totalSellTrades = 0;
int totalWinningTrades = 0;
int totalLosingTrades = 0;
double totalProfit = 0.0;
double totalLoss = 0.0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("تم تشغيل إكسبرت Vagues EA");
   Print("البحث عن مستويات فيبوناتشي الحمراء والخضراء...");

   // طباعة جميع الكائنات الموجودة على الشارت لمساعدة في التشخيص
   PrintAllChartObjects();

   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Print("تم إيقاف إكسبرت Vagues EA");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // التحقق من وجود إشارات جديدة باستخدام الدوال المحسنة
   bool buySignal = CheckBuySignalAdvanced();
   bool sellSignal = CheckSellSignalAdvanced();

   // فتح صفقة شراء عند الإشارة
   if(buySignal && !lastBuySignal && TimeCurrent() - lastTradeTime > minTimeBetweenTrades)
   {
      // التحقق من عدم وجود صفقات شراء مفتوحة إذا كان مفعل OneTradePerSignal
      bool canOpenBuy = true;
      if(OneTradePerSignal && HasOpenTradesOfType(OP_BUY))
      {
         canOpenBuy = false;
         Print("تم تجاهل إشارة الشراء - يوجد صفقة شراء مفتوحة بالفعل");
      }

      if(canOpenBuy)
      {
         // إغلاق صفقات البيع المفتوحة (إشارة معاكسة)
         if(CloseOppositeSignals && HasOpenTradesOfType(OP_SELL))
         {
            CloseTradesOfType(OP_SELL);
            Print("تم إغلاق صفقات البيع بسبب إشارة شراء جديدة");
         }

         if(OpenBuyTrade())
         {
            lastTradeTime = TimeCurrent();
            Print("تم فتح صفقة شراء عند ملامسة مستوى فيبوناتشي الأحمر 61.8%");
         }
      }
   }

   // فتح صفقة بيع عند الإشارة
   if(sellSignal && !lastSellSignal && TimeCurrent() - lastTradeTime > minTimeBetweenTrades)
   {
      // التحقق من عدم وجود صفقات بيع مفتوحة إذا كان مفعل OneTradePerSignal
      bool canOpenSell = true;
      if(OneTradePerSignal && HasOpenTradesOfType(OP_SELL))
      {
         canOpenSell = false;
         Print("تم تجاهل إشارة البيع - يوجد صفقة بيع مفتوحة بالفعل");
      }

      if(canOpenSell)
      {
         // إغلاق صفقات الشراء المفتوحة (إشارة معاكسة)
         if(CloseOppositeSignals && HasOpenTradesOfType(OP_BUY))
         {
            CloseTradesOfType(OP_BUY);
            Print("تم إغلاق صفقات الشراء بسبب إشارة بيع جديدة");
         }

         if(OpenSellTrade())
         {
            lastTradeTime = TimeCurrent();
            Print("تم فتح صفقة بيع عند ملامسة مستوى فيبوناتشي الأخضر 61.8%");
         }
      }
   }

   // حفظ حالة الإشارات
   lastBuySignal = buySignal;
   lastSellSignal = sellSignal;

   // عرض معلومات على الشارت
   ShowChartInfo();

   // طباعة تفاصيل مستويات فيبوناتشي كل 10 ثوان
   static datetime lastPrintTime = 0;
   if(ShowDebugInfo && TimeCurrent() - lastPrintTime > 10)
   {
      PrintFibonacciDetails();
      lastPrintTime = TimeCurrent();
   }
}

//+------------------------------------------------------------------+
//| التحقق من إشارة الشراء (فيبوناتشي أحمر + ملامسة 61.8%)           |
//+------------------------------------------------------------------+
bool CheckBuySignal()
{
   return IsFibonacciLevelTouched(FiboLevel, RedFiboColor, true);
}

//+------------------------------------------------------------------+
//| التحقق من إشارة البيع (فيبوناتشي أخضر + ملامسة 61.8%)            |
//+------------------------------------------------------------------+
bool CheckSellSignal()
{
   return IsFibonacciLevelTouched(FiboLevel, GreenFiboColor, false);
}

//+------------------------------------------------------------------+
//| التحقق من ملامسة مستوى فيبوناتشي - طريقة محسنة                   |
//+------------------------------------------------------------------+
bool IsFibonacciLevelTouched(double level, color levelColor, bool isBuySignal)
{
   // استخدام الدالة المحسنة للبحث
   double levels[10];
   int count = 0;

   if(FindVaguesFibonacciLevels(levelColor, levels, count))
   {
      return CheckPriceTouchesAnyLevel(levels, count, isBuySignal);
   }

   return false;
}

//+------------------------------------------------------------------+
//| البحث عن مستويات فيبوناتشي من مؤشر Vagues - محسن ودقيق          |
//+------------------------------------------------------------------+
bool FindVaguesFibonacciLevels(color targetColor, double &levelPrices[], int &count)
{
   count = 0;
   int total = ObjectsTotal();

   Print("البحث عن مستويات فيبوناتشي باللون: ", targetColor, " من إجمالي ", total, " كائن");

   for(int i = total-1; i >= 0; i--)
   {
      string name = ObjectName(i);
      int objType = ObjectType(name);

      // البحث عن كائنات فيبوناتشي من مؤشر Vagues
      bool isVaguesFibo = false;

      // فحص الأسماء المحتملة لمؤشر Vagues
      if(StringFind(name, "MyFibo") >= 0 || StringFind(name, "Target") >= 0 ||
         StringFind(name, "Fibo") >= 0 || StringFind(name, "fibo") >= 0 ||
         StringFind(name, "Fibonacci") >= 0 || StringFind(name, "fibonacci") >= 0)
      {
         isVaguesFibo = true;
      }

      // فحص أنواع كائنات فيبوناتشي
      if(objType == OBJ_FIBO || objType == OBJ_FIBOFAN ||
         objType == OBJ_FIBOARC || objType == OBJ_FIBOCHANNEL)
      {
         isVaguesFibo = true;
      }

      if(isVaguesFibo)
      {
         color objColor = (color)ObjectGet(name, OBJPROP_COLOR);

         Print("كائن فيبوناتشي مكتشف: ", name, " | النوع: ", objType, " | اللون: ", objColor);

         // التحقق الدقيق من اللون - يجب أن يطابق تماماً
         bool colorMatches = false;
         if(targetColor == (color)clrNONE)
         {
            colorMatches = true; // قبول جميع الألوان
         }
         else if(objColor == targetColor)
         {
            colorMatches = true; // تطابق دقيق للون
            Print("  -> تطابق اللون! اللون المطلوب: ", targetColor, " | اللون الموجود: ", objColor);
         }

         if(colorMatches)
         {
            double price1 = ObjectGet(name, OBJPROP_PRICE1);
            double price2 = ObjectGet(name, OBJPROP_PRICE2);

            Print("  -> أسعار الكائن: السعر1=", DoubleToString(price1, Digits), " | السعر2=", DoubleToString(price2, Digits));

            if(price1 > 0 && price2 > 0 && price1 != price2)
            {
               // حساب مستوى 61.8% بدقة
               double levelPrice = price1 + (price2 - price1) * FiboLevel;

               Print("  -> حساب مستوى ", DoubleToString(FiboLevel*100, 1), "%: ", DoubleToString(levelPrice, Digits));

               // إضافة المستوى إلى المصفوفة
               if(count < ArraySize(levelPrices))
               {
                  levelPrices[count] = levelPrice;
                  count++;
                  Print("  ✅ تم إضافة مستوى فيبوناتشي صالح: ", DoubleToString(levelPrice, Digits));
               }
            }
            else
            {
               Print("  ❌ أسعار غير صالحة للكائن: ", name);
            }
         }
         else
         {
            Print("  ❌ اللون لا يطابق. المطلوب: ", targetColor, " | الموجود: ", objColor);
         }
      }
   }

   Print("انتهاء البحث. تم العثور على ", count, " مستوى فيبوناتشي صالح");
   return (count > 0);
}

//+------------------------------------------------------------------+
//| فتح صفقة شراء                                                    |
//+------------------------------------------------------------------+
bool OpenBuyTrade()
{
   double price = Ask;
   double sl = 0, tp = 0;
   
   if(UseStopLoss && StopLoss > 0)
      sl = price - StopLoss * Point;
   
   if(UseTakeProfit && TakeProfit > 0)
      tp = price + TakeProfit * Point;
   
   int ticket = OrderSend(Symbol(), OP_BUY, LotSize, price, Slippage, sl, tp,
                         "Vagues EA Buy Signal", MagicNumber, 0, clrGreen);
   
   if(ticket > 0)
   {
      totalBuyTrades++;
      Print("تم فتح صفقة شراء - التذكرة: ", ticket);
      if(EnableAlerts)
         Alert("تم فتح صفقة شراء - التذكرة: ", ticket);
      return true;
   }
   else
   {
      Print("فشل في فتح صفقة الشراء - الخطأ: ", GetLastError());
      if(EnableAlerts)
         Alert("فشل في فتح صفقة الشراء - الخطأ: ", GetLastError());
      return false;
   }
}

//+------------------------------------------------------------------+
//| فتح صفقة بيع                                                     |
//+------------------------------------------------------------------+
bool OpenSellTrade()
{
   double price = Bid;
   double sl = 0, tp = 0;
   
   if(UseStopLoss && StopLoss > 0)
      sl = price + StopLoss * Point;
   
   if(UseTakeProfit && TakeProfit > 0)
      tp = price - TakeProfit * Point;
   
   int ticket = OrderSend(Symbol(), OP_SELL, LotSize, price, Slippage, sl, tp,
                         "Vagues EA Sell Signal", MagicNumber, 0, clrRed);
   
   if(ticket > 0)
   {
      totalSellTrades++;
      Print("تم فتح صفقة بيع - التذكرة: ", ticket);
      if(EnableAlerts)
         Alert("تم فتح صفقة بيع - التذكرة: ", ticket);
      return true;
   }
   else
   {
      Print("فشل في فتح صفقة البيع - الخطأ: ", GetLastError());
      if(EnableAlerts)
         Alert("فشل في فتح صفقة البيع - الخطأ: ", GetLastError());
      return false;
   }
}

//+------------------------------------------------------------------+
//| عرض معلومات على الشارت                                          |
//+------------------------------------------------------------------+
void ShowChartInfo()
{
   string info = "";
   info += "=== إكسبرت Vagues EA ===\n";
   info += "البحث عن مستويات فيبوناتشي...\n";
   info += "مستوى فيبوناتشي: " + DoubleToString(FiboLevel * 100, 1) + "%\n";
   info += "التسامح: " + DoubleToString(FiboTolerance, 1) + " نقطة\n";
   info += "لون الشراء (أحمر): " + ColorToString(RedFiboColor) + "\n";
   info += "لون البيع (أخضر): " + ColorToString(GreenFiboColor) + "\n";
   info += "السعر الحالي: " + DoubleToString((Bid + Ask) / 2.0, Digits) + "\n";

   // عد الصفقات حسب النوع
   int buyTrades = 0, sellTrades = 0;
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS) && OrderSymbol() == Symbol() &&
         OrderMagicNumber() == MagicNumber)
      {
         if(OrderType() == OP_BUY) buyTrades++;
         else if(OrderType() == OP_SELL) sellTrades++;
      }
   }

   info += "صفقات الشراء المفتوحة: " + IntegerToString(buyTrades) + "\n";
   info += "صفقات البيع المفتوحة: " + IntegerToString(sellTrades) + "\n";
   info += "إجمالي الصفقات: " + IntegerToString(buyTrades + sellTrades) + "\n";
   info += "إجمالي الربح: " + DoubleToString(GetTotalProfit(), 2) + "\n";

   // البحث عن مستويات فيبوناتشي الحالية من مؤشر Vagues
   double redLevels[10], greenLevels[10];
   int redCount = 0, greenCount = 0;

   FindVaguesFibonacciLevels(RedFiboColor, redLevels, redCount);
   FindVaguesFibonacciLevels(GreenFiboColor, greenLevels, greenCount);

   info += "مستويات فيبوناتشي الحمراء: " + IntegerToString(redCount) + "\n";
   info += "مستويات فيبوناتشي الخضراء: " + IntegerToString(greenCount) + "\n";

   // عرض أقرب المستويات
   if(redCount > 0)
   {
      double currentPrice = (Bid + Ask) / 2.0;
      double closestRed = redLevels[0];
      double minDistance = MathAbs(currentPrice - closestRed);

      for(int i = 1; i < redCount; i++)
      {
         double distance = MathAbs(currentPrice - redLevels[i]);
         if(distance < minDistance)
         {
            minDistance = distance;
            closestRed = redLevels[i];
         }
      }

      info += "أقرب مستوى أحمر: " + DoubleToString(closestRed, Digits) +
              " (المسافة: " + DoubleToString(minDistance / Point, 1) + " نقطة)\n";
   }

   if(greenCount > 0)
   {
      double currentPrice = (Bid + Ask) / 2.0;
      double closestGreen = greenLevels[0];
      double minDistance = MathAbs(currentPrice - closestGreen);

      for(int i = 1; i < greenCount; i++)
      {
         double distance = MathAbs(currentPrice - greenLevels[i]);
         if(distance < minDistance)
         {
            minDistance = distance;
            closestGreen = greenLevels[i];
         }
      }

      info += "أقرب مستوى أخضر: " + DoubleToString(closestGreen, Digits) +
              " (المسافة: " + DoubleToString(minDistance / Point, 1) + " نقطة)\n";
   }

   // عرض حالة الإشارات
   info += "إشارة الشراء: " + (lastBuySignal ? "نشطة" : "غير نشطة") + "\n";
   info += "إشارة البيع: " + (lastSellSignal ? "نشطة" : "غير نشطة") + "\n";

   // إحصائيات التداول
   info += "\n=== إحصائيات التداول ===\n";
   info += "إجمالي صفقات الشراء: " + IntegerToString(totalBuyTrades) + "\n";
   info += "إجمالي صفقات البيع: " + IntegerToString(totalSellTrades) + "\n";
   info += "إجمالي الصفقات: " + IntegerToString(totalBuyTrades + totalSellTrades) + "\n";

   // حساب نسبة النجاح
   int totalTrades = totalBuyTrades + totalSellTrades;
   if(totalTrades > 0)
   {
      double successRate = (double)(totalWinningTrades) / totalTrades * 100.0;
      info += "نسبة النجاح: " + DoubleToString(successRate, 1) + "%\n";
   }

   Comment(info);
}

//+------------------------------------------------------------------+
//| عد الصفقات المفتوحة                                              |
//+------------------------------------------------------------------+
int CountOpenTrades()
{
   int count = 0;
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS) && OrderSymbol() == Symbol() && 
         OrderMagicNumber() == MagicNumber)
      {
         count++;
      }
   }
   return count;
}

//+------------------------------------------------------------------+
//| حساب إجمالي الربح                                               |
//+------------------------------------------------------------------+
double GetTotalProfit()
{
   double profit = 0;
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS) && OrderSymbol() == Symbol() &&
         OrderMagicNumber() == MagicNumber)
      {
         profit += OrderProfit() + OrderSwap() + OrderCommission();
      }
   }
   return profit;
}

//+------------------------------------------------------------------+
//| البحث المتقدم عن مستويات فيبوناتشي بألوان محددة                  |
//+------------------------------------------------------------------+
bool FindFibonacciLevelsByColor(color targetColor, double &levelPrices[], int &count)
{
   count = 0;
   int total = ObjectsTotal();

   for(int i = total-1; i >= 0; i--)
   {
      string name = ObjectName(i);

      // البحث عن كائنات فيبوناتشي من مؤشر Vagues
      // أسماء محتملة: MyFibo_sell, MyFibo_buy, Target_up, Target_down
      if(StringFind(name, "MyFibo") >= 0 || StringFind(name, "Target") >= 0 ||
         StringFind(name, "Fibo") >= 0 || StringFind(name, "fibo") >= 0 ||
         ObjectType(name) == OBJ_FIBO ||
         StringFind(name, "Fibonacci") >= 0 || StringFind(name, "fibonacci") >= 0)
      {
         // التحقق من لون الكائن
         if(ObjectGet(name, OBJPROP_COLOR) == targetColor)
         {
            double price1 = ObjectGet(name, OBJPROP_PRICE1);
            double price2 = ObjectGet(name, OBJPROP_PRICE2);

            // حساب مستوى 61.8%
            double levelPrice = price1 + (price2 - price1) * FiboLevel;

            // إضافة المستوى إلى المصفوفة
            if(count < ArraySize(levelPrices))
            {
               levelPrices[count] = levelPrice;
               count++;
               Print("تم العثور على مستوى فيبوناتشي: ", name, " - اللون: ", targetColor, " - المستوى: ", DoubleToString(levelPrice, Digits));
            }
         }
      }
   }

   return (count > 0);
}

//+------------------------------------------------------------------+
//| طباعة جميع الكائنات الموجودة على الشارت                         |
//+------------------------------------------------------------------+
void PrintAllChartObjects()
{
   int total = ObjectsTotal();
   Print("=== جميع الكائنات على الشارت (", total, ") ===");

   for(int i = 0; i < total; i++)
   {
      string name = ObjectName(i);
      int type = ObjectType(name);
      color objColor = (color)ObjectGet(name, OBJPROP_COLOR);

      string typeStr = "";
      switch(type)
      {
         case OBJ_VLINE: typeStr = "خط عمودي"; break;
         case OBJ_HLINE: typeStr = "خط أفقي"; break;
         case OBJ_TREND: typeStr = "خط اتجاه"; break;
         case OBJ_FIBO: typeStr = "فيبوناتشي"; break;
         case OBJ_FIBOFAN: typeStr = "فيبوناتشي مروحة"; break;
         case OBJ_FIBOARC: typeStr = "فيبوناتشي قوس"; break;
         case OBJ_FIBOCHANNEL: typeStr = "فيبوناتشي قناة"; break;
         case OBJ_ARROW: typeStr = "سهم"; break;
         case OBJ_TEXT: typeStr = "نص"; break;
         case OBJ_LABEL: typeStr = "تسمية"; break;
         case OBJ_TRIANGLE: typeStr = "مثلث"; break;
         case OBJ_RECTANGLE: typeStr = "مستطيل"; break;
         default: typeStr = "نوع " + IntegerToString(type); break;
      }

      Print("الكائن ", i+1, ": الاسم=", name, " | النوع=", typeStr, " | اللون=", objColor);

      // إذا كان فيبوناتشي، اطبع تفاصيل إضافية
      if(type == OBJ_FIBO)
      {
         double price1 = ObjectGet(name, OBJPROP_PRICE1);
         double price2 = ObjectGet(name, OBJPROP_PRICE2);
         datetime time1 = (datetime)ObjectGet(name, OBJPROP_TIME1);
         datetime time2 = (datetime)ObjectGet(name, OBJPROP_TIME2);

         Print("   تفاصيل فيبوناتشي: السعر1=", DoubleToString(price1, Digits),
               " | السعر2=", DoubleToString(price2, Digits),
               " | الوقت1=", TimeToString(time1),
               " | الوقت2=", TimeToString(time2));

         // حساب مستوى 61.8%
         double level618 = price1 + (price2 - price1) * 0.618;
         Print("   مستوى 61.8% = ", DoubleToString(level618, Digits));
      }
   }

   Print("=== انتهاء قائمة الكائنات ===");
}

//+------------------------------------------------------------------+
//| طباعة تفاصيل مستويات فيبوناتشي المكتشفة                        |
//+------------------------------------------------------------------+
void PrintFibonacciDetails()
{
   double redLevels[10], greenLevels[10];
   int redCount = 0, greenCount = 0;

   Print("=== تفاصيل مستويات فيبوناتشي ===");

   // البحث عن المستويات الحمراء
   if(FindVaguesFibonacciLevels(RedFiboColor, redLevels, redCount))
   {
      Print("مستويات فيبوناتشي الحمراء (", redCount, "):");
      for(int i = 0; i < redCount; i++)
      {
         double distance = MathAbs(Ask - redLevels[i]) / Point;
         Print("  المستوى ", i+1, ": ", DoubleToString(redLevels[i], Digits),
               " (المسافة: ", DoubleToString(distance, 1), " نقطة)");
      }
   }
   else
   {
      Print("لم يتم العثور على مستويات فيبوناتشي حمراء");
   }

   // البحث عن المستويات الخضراء
   if(FindVaguesFibonacciLevels(GreenFiboColor, greenLevels, greenCount))
   {
      Print("مستويات فيبوناتشي الخضراء (", greenCount, "):");
      for(int i = 0; i < greenCount; i++)
      {
         double distance = MathAbs(Bid - greenLevels[i]) / Point;
         Print("  المستوى ", i+1, ": ", DoubleToString(greenLevels[i], Digits),
               " (المسافة: ", DoubleToString(distance, 1), " نقطة)");
      }
   }
   else
   {
      Print("لم يتم العثور على مستويات فيبوناتشي خضراء");
   }

   // البحث عن جميع مستويات فيبوناتشي بدون تحديد لون
   double allLevels[20];
   int allCount = 0;
   if(FindVaguesFibonacciLevels((color)clrNONE, allLevels, allCount))
   {
      Print("إجمالي مستويات فيبوناتشي المكتشفة: ", allCount);
   }

   Print("السعر الحالي: Ask=", DoubleToString(Ask, Digits), " | Bid=", DoubleToString(Bid, Digits));
   Print("=== انتهاء تفاصيل فيبوناتشي ===");
}

//+------------------------------------------------------------------+
//| التحقق من ملامسة السعر لأي من مستويات فيبوناتشي                  |
//+------------------------------------------------------------------+
bool CheckPriceTouchesAnyLevel(double &levels[], int count, bool isBuySignal)
{
   double currentPrice = isBuySignal ? Ask : Bid;
   double tolerance = FiboTolerance * Point;

   for(int i = 0; i < count; i++)
   {
      double levelPrice = levels[i];

      if(isBuySignal)
      {
         // إشارة شراء: السعر يلامس المستوى من الأسفل
         if(currentPrice >= levelPrice - tolerance &&
            currentPrice <= levelPrice + tolerance &&
            Low[1] < levelPrice - tolerance)
         {
            Print("تم اكتشاف ملامسة مستوى فيبوناتشي للشراء عند: ", DoubleToString(levelPrice, Digits));
            return true;
         }
      }
      else
      {
         // إشارة بيع: السعر يلامس المستوى من الأعلى
         if(currentPrice >= levelPrice - tolerance &&
            currentPrice <= levelPrice + tolerance &&
            High[1] > levelPrice + tolerance)
         {
            Print("تم اكتشاف ملامسة مستوى فيبوناتشي للبيع عند: ", DoubleToString(levelPrice, Digits));
            return true;
         }
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| التحقق من وجود صفقات مفتوحة من نفس النوع                        |
//+------------------------------------------------------------------+
bool HasOpenTradesOfType(int orderType)
{
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS) && OrderSymbol() == Symbol() &&
         OrderMagicNumber() == MagicNumber && OrderType() == orderType)
      {
         return true;
      }
   }
   return false;
}

//+------------------------------------------------------------------+
//| إغلاق جميع الصفقات من نوع معين                                  |
//+------------------------------------------------------------------+
void CloseTradesOfType(int orderType)
{
   for(int i = OrdersTotal()-1; i >= 0; i--)
   {
      if(OrderSelect(i, SELECT_BY_POS) && OrderSymbol() == Symbol() &&
         OrderMagicNumber() == MagicNumber && OrderType() == orderType)
      {
         bool result = false;
         if(orderType == OP_BUY)
            result = OrderClose(OrderTicket(), OrderLots(), Bid, Slippage, clrRed);
         else if(orderType == OP_SELL)
            result = OrderClose(OrderTicket(), OrderLots(), Ask, Slippage, clrBlue);

         if(result)
         {
            // تحديث الإحصائيات
            double profit = OrderProfit() + OrderSwap() + OrderCommission();
            if(profit > 0)
            {
               totalWinningTrades++;
               totalProfit += profit;
            }
            else
            {
               totalLosingTrades++;
               totalLoss += MathAbs(profit);
            }

            Print("تم إغلاق الصفقة رقم: ", OrderTicket(), " | الربح: ", DoubleToString(profit, 2));
         }
         else
            Print("فشل في إغلاق الصفقة رقم: ", OrderTicket(), " - الخطأ: ", GetLastError());
      }
   }
}

//+------------------------------------------------------------------+
//| تحديث دالة التحقق من إشارة الشراء                               |
//+------------------------------------------------------------------+
bool CheckBuySignalAdvanced()
{
   double redLevels[10];
   int redCount = 0;

   // البحث عن مستويات فيبوناتشي الحمراء من مؤشر Vagues
   if(FindVaguesFibonacciLevels(RedFiboColor, redLevels, redCount))
   {
      // التحقق من ملامسة السعر لأي من المستويات
      return CheckPriceTouchesAnyLevel(redLevels, redCount, true);
   }

   // البحث البديل بدون تحديد لون
   if(FindVaguesFibonacciLevels((color)clrNONE, redLevels, redCount))
   {
      // فحص كل مستوى للتأكد من أنه مناسب للشراء
      for(int i = 0; i < redCount; i++)
      {
         if(CheckSingleLevelForBuy(redLevels[i]))
         {
            if(EnableAlerts)
               Alert("إشارة شراء: ملامسة مستوى فيبوناتشي أحمر عند ", DoubleToString(redLevels[i], Digits));
            return true;
         }
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| تحديث دالة التحقق من إشارة البيع                                |
//+------------------------------------------------------------------+
bool CheckSellSignalAdvanced()
{
   double greenLevels[10];
   int greenCount = 0;

   // البحث عن مستويات فيبوناتشي الخضراء من مؤشر Vagues
   if(FindVaguesFibonacciLevels(GreenFiboColor, greenLevels, greenCount))
   {
      // التحقق من ملامسة السعر لأي من المستويات
      return CheckPriceTouchesAnyLevel(greenLevels, greenCount, false);
   }

   // البحث البديل بدون تحديد لون
   if(FindVaguesFibonacciLevels((color)clrNONE, greenLevels, greenCount))
   {
      // فحص كل مستوى للتأكد من أنه مناسب للبيع
      for(int i = 0; i < greenCount; i++)
      {
         if(CheckSingleLevelForSell(greenLevels[i]))
         {
            if(EnableAlerts)
               Alert("إشارة بيع: ملامسة مستوى فيبوناتشي أخضر عند ", DoubleToString(greenLevels[i], Digits));
            return true;
         }
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| فحص مستوى واحد لإشارة الشراء                                    |
//+------------------------------------------------------------------+
bool CheckSingleLevelForBuy(double levelPrice)
{
   double currentPrice = Ask;
   double tolerance = FiboTolerance * Point;

   // إشارة شراء: السعر يلامس المستوى من الأسفل
   if(currentPrice >= levelPrice - tolerance &&
      currentPrice <= levelPrice + tolerance &&
      Low[1] < levelPrice - tolerance)
   {
      Print("تم اكتشاف ملامسة مستوى فيبوناتشي للشراء عند: ", DoubleToString(levelPrice, Digits));
      return true;
   }

   return false;
}

//+------------------------------------------------------------------+
//| فحص مستوى واحد لإشارة البيع                                     |
//+------------------------------------------------------------------+
bool CheckSingleLevelForSell(double levelPrice)
{
   double currentPrice = Bid;
   double tolerance = FiboTolerance * Point;

   // إشارة بيع: السعر يلامس المستوى من الأعلى
   if(currentPrice >= levelPrice - tolerance &&
      currentPrice <= levelPrice + tolerance &&
      High[1] > levelPrice + tolerance)
   {
      Print("تم اكتشاف ملامسة مستوى فيبوناتشي للبيع عند: ", DoubleToString(levelPrice, Digits));
      return true;
   }

   return false;
}
