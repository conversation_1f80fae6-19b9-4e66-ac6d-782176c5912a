//+------------------------------------------------------------------+
//|                                                      VaguesEA.mq4|
//|                        إكسبرت مبني على مؤشر Vagues               |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Alsayaf EA"
#property link      ""
#property version   "1.00"
#property strict

//--- Input parameters
input group "=== إعدادات التداول ==="
input double LotSize = 0.1;           // حجم اللوت
input int MagicNumber = 12345;        // الرقم السحري
input int Slippage = 3;               // الانزلاق
input int StopLoss = 100;             // وقف الخسارة بالنقاط
input int TakeProfit = 200;           // جني الأرباح بالنقاط
input bool UseStopLoss = true;        // استخدام وقف الخسارة
input bool UseTakeProfit = true;      // استخدام جني الأرباح

input group "=== إعدادات مؤشر Vagues ==="
input int History = 1000;             // عدد الشموع للتحليل
input int Begin_draw_down = 5;        // بداية الرسم للأسفل
input int Begin_draw_up = 5;          // بداية الرسم للأعلى
input int Sektor_bar = 13;            // شموع القطاع
input int f_period_draw = 1;          // فترة الرسم
input int clear_opposite = 4;         // مسح المقابل
input int Wawe_bar = 21;              // شموع الموجة
input bool f_draw_wawe_fibo = true;   // رسم فيبوناتشي الموجة
input bool f_draw_target_level = true; // رسم مستوى الهدف
input int DM_sdvig = 7;               // إزاحة DM
input int DM_xvost = 10;              // ذيل DM
input bool f_draw_mini_trend = false; // رسم الاتجاه المصغر

input group "=== إعدادات فيبوناتشي ==="
input double FiboLevel = 0.618;       // مستوى فيبوناتشي للدخول (61.8%)
input double FiboTolerance = 5.0;     // التسامح في النقاط لملامسة المستوى
input color RedFiboColor = clrRed;    // لون فيبوناتشي الأحمر (إشارة شراء)
input color GreenFiboColor = clrLime; // لون فيبوناتشي الأخضر (إشارة بيع)
input bool CloseOppositeSignals = true; // إغلاق الصفقات المعاكسة عند الإشارة الجديدة
input bool OneTradePerSignal = true;  // صفقة واحدة فقط لكل إشارة

//--- Global variables
bool lastBuySignal = false;
bool lastSellSignal = false;
datetime lastTradeTime = 0;
int minTimeBetweenTrades = 60; // دقيقة واحدة بين الصفقات

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("تم تشغيل إكسبرت Vagues EA");
   Print("البحث عن مستويات فيبوناتشي الحمراء والخضراء...");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
int OnDeinit()
{
   Print("تم إيقاف إكسبرت Vagues EA");
   return(0);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
int OnTick()
{
   // التحقق من وجود إشارات جديدة باستخدام الدوال المحسنة
   bool buySignal = CheckBuySignalAdvanced();
   bool sellSignal = CheckSellSignalAdvanced();

   // فتح صفقة شراء عند الإشارة
   if(buySignal && !lastBuySignal && TimeCurrent() - lastTradeTime > minTimeBetweenTrades)
   {
      // التحقق من عدم وجود صفقات شراء مفتوحة إذا كان مفعل OneTradePerSignal
      bool canOpenBuy = true;
      if(OneTradePerSignal && HasOpenTradesOfType(OP_BUY))
      {
         canOpenBuy = false;
         Print("تم تجاهل إشارة الشراء - يوجد صفقة شراء مفتوحة بالفعل");
      }

      if(canOpenBuy)
      {
         // إغلاق صفقات البيع المفتوحة (إشارة معاكسة)
         if(CloseOppositeSignals && HasOpenTradesOfType(OP_SELL))
         {
            CloseTradesOfType(OP_SELL);
            Print("تم إغلاق صفقات البيع بسبب إشارة شراء جديدة");
         }

         if(OpenBuyTrade())
         {
            lastTradeTime = TimeCurrent();
            Print("تم فتح صفقة شراء عند ملامسة مستوى فيبوناتشي الأحمر 61.8%");
         }
      }
   }

   // فتح صفقة بيع عند الإشارة
   if(sellSignal && !lastSellSignal && TimeCurrent() - lastTradeTime > minTimeBetweenTrades)
   {
      // التحقق من عدم وجود صفقات بيع مفتوحة إذا كان مفعل OneTradePerSignal
      bool canOpenSell = true;
      if(OneTradePerSignal && HasOpenTradesOfType(OP_SELL))
      {
         canOpenSell = false;
         Print("تم تجاهل إشارة البيع - يوجد صفقة بيع مفتوحة بالفعل");
      }

      if(canOpenSell)
      {
         // إغلاق صفقات الشراء المفتوحة (إشارة معاكسة)
         if(CloseOppositeSignals && HasOpenTradesOfType(OP_BUY))
         {
            CloseTradesOfType(OP_BUY);
            Print("تم إغلاق صفقات الشراء بسبب إشارة بيع جديدة");
         }

         if(OpenSellTrade())
         {
            lastTradeTime = TimeCurrent();
            Print("تم فتح صفقة بيع عند ملامسة مستوى فيبوناتشي الأخضر 61.8%");
         }
      }
   }

   // حفظ حالة الإشارات
   lastBuySignal = buySignal;
   lastSellSignal = sellSignal;

   // عرض معلومات على الشارت
   ShowChartInfo();

   return(0);
}

//+------------------------------------------------------------------+
//| التحقق من إشارة الشراء (فيبوناتشي أحمر + ملامسة 61.8%)           |
//+------------------------------------------------------------------+
bool CheckBuySignal()
{
   return IsFibonacciLevelTouched(FiboLevel, RedFiboColor, true);
}

//+------------------------------------------------------------------+
//| التحقق من إشارة البيع (فيبوناتشي أخضر + ملامسة 61.8%)            |
//+------------------------------------------------------------------+
bool CheckSellSignal()
{
   return IsFibonacciLevelTouched(FiboLevel, GreenFiboColor, false);
}

//+------------------------------------------------------------------+
//| التحقق من ملامسة مستوى فيبوناتشي                                |
//+------------------------------------------------------------------+
bool IsFibonacciLevelTouched(double level, color levelColor, bool isBuySignal)
{
   int total = ObjectsTotal();
   
   for(int i = total-1; i >= 0; i--)
   {
      string name = ObjectName(i);
      
      // البحث عن كائنات فيبوناتشي
      if(StringFind(name, "Fibo") >= 0 || StringFind(name, "fibo") >= 0 || 
         StringFind(name, "MyFibo") >= 0 || ObjectType(name) == OBJ_FIBO)
      {
         // التحقق من لون المستوى
         if(ObjectGet(name, OBJPROP_COLOR) == levelColor)
         {
            double price1 = ObjectGet(name, OBJPROP_PRICE1);
            double price2 = ObjectGet(name, OBJPROP_PRICE2);
            
            // حساب مستوى فيبوناتشي المطلوب
            double levelPrice = price1 + (price2 - price1) * level;
            
            // التحقق من ملامسة السعر للمستوى
            double currentPrice = isBuySignal ? Ask : Bid;
            double tolerance = FiboTolerance * Point;
            
            if(isBuySignal)
            {
               // إشارة شراء: السعر يلامس أو يقترب من المستوى من الأسفل
               if(currentPrice >= levelPrice - tolerance && 
                  currentPrice <= levelPrice + tolerance &&
                  Low[1] < levelPrice)
               {
                  return true;
               }
            }
            else
            {
               // إشارة بيع: السعر يلامس أو يقترب من المستوى من الأعلى
               if(currentPrice >= levelPrice - tolerance && 
                  currentPrice <= levelPrice + tolerance &&
                  High[1] > levelPrice)
               {
                  return true;
               }
            }
         }
      }
   }
   
   return false;
}

//+------------------------------------------------------------------+
//| فتح صفقة شراء                                                    |
//+------------------------------------------------------------------+
bool OpenBuyTrade()
{
   double price = Ask;
   double sl = 0, tp = 0;
   
   if(UseStopLoss && StopLoss > 0)
      sl = price - StopLoss * Point;
   
   if(UseTakeProfit && TakeProfit > 0)
      tp = price + TakeProfit * Point;
   
   int ticket = OrderSend(Symbol(), OP_BUY, LotSize, price, Slippage, sl, tp,
                         "Vagues EA Buy Signal", MagicNumber, 0, clrGreen);
   
   if(ticket > 0)
   {
      Print("تم فتح صفقة شراء - التذكرة: ", ticket);
      return true;
   }
   else
   {
      Print("فشل في فتح صفقة الشراء - الخطأ: ", GetLastError());
      return false;
   }
}

//+------------------------------------------------------------------+
//| فتح صفقة بيع                                                     |
//+------------------------------------------------------------------+
bool OpenSellTrade()
{
   double price = Bid;
   double sl = 0, tp = 0;
   
   if(UseStopLoss && StopLoss > 0)
      sl = price + StopLoss * Point;
   
   if(UseTakeProfit && TakeProfit > 0)
      tp = price - TakeProfit * Point;
   
   int ticket = OrderSend(Symbol(), OP_SELL, LotSize, price, Slippage, sl, tp,
                         "Vagues EA Sell Signal", MagicNumber, 0, clrRed);
   
   if(ticket > 0)
   {
      Print("تم فتح صفقة بيع - التذكرة: ", ticket);
      return true;
   }
   else
   {
      Print("فشل في فتح صفقة البيع - الخطأ: ", GetLastError());
      return false;
   }
}

//+------------------------------------------------------------------+
//| عرض معلومات على الشارت                                          |
//+------------------------------------------------------------------+
void ShowChartInfo()
{
   string info = "";
   info += "=== إكسبرت Vagues EA ===\n";
   info += "البحث عن مستويات فيبوناتشي...\n";
   info += "مستوى فيبوناتشي: " + DoubleToString(FiboLevel * 100, 1) + "%\n";
   info += "التسامح: " + DoubleToString(FiboTolerance, 1) + " نقطة\n";
   info += "لون الشراء (أحمر): " + ColorToString(RedFiboColor) + "\n";
   info += "لون البيع (أخضر): " + ColorToString(GreenFiboColor) + "\n";
   info += "السعر الحالي: " + DoubleToString((Bid + Ask) / 2.0, Digits) + "\n";

   // عد الصفقات حسب النوع
   int buyTrades = 0, sellTrades = 0;
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS) && OrderSymbol() == Symbol() &&
         OrderMagicNumber() == MagicNumber)
      {
         if(OrderType() == OP_BUY) buyTrades++;
         else if(OrderType() == OP_SELL) sellTrades++;
      }
   }

   info += "صفقات الشراء المفتوحة: " + IntegerToString(buyTrades) + "\n";
   info += "صفقات البيع المفتوحة: " + IntegerToString(sellTrades) + "\n";
   info += "إجمالي الصفقات: " + IntegerToString(buyTrades + sellTrades) + "\n";
   info += "إجمالي الربح: " + DoubleToString(GetTotalProfit(), 2) + "\n";

   // البحث عن مستويات فيبوناتشي الحالية
   double redLevels[10], greenLevels[10];
   int redCount = 0, greenCount = 0;

   FindFibonacciLevelsByColor(RedFiboColor, redLevels, redCount);
   FindFibonacciLevelsByColor(GreenFiboColor, greenLevels, greenCount);

   info += "مستويات فيبوناتشي الحمراء: " + IntegerToString(redCount) + "\n";
   info += "مستويات فيبوناتشي الخضراء: " + IntegerToString(greenCount) + "\n";

   // عرض أقرب المستويات
   if(redCount > 0)
   {
      double currentPrice = (Bid + Ask) / 2.0;
      double closestRed = redLevels[0];
      double minDistance = MathAbs(currentPrice - closestRed);

      for(int i = 1; i < redCount; i++)
      {
         double distance = MathAbs(currentPrice - redLevels[i]);
         if(distance < minDistance)
         {
            minDistance = distance;
            closestRed = redLevels[i];
         }
      }

      info += "أقرب مستوى أحمر: " + DoubleToString(closestRed, Digits) +
              " (المسافة: " + DoubleToString(minDistance / Point, 1) + " نقطة)\n";
   }

   if(greenCount > 0)
   {
      double currentPrice = (Bid + Ask) / 2.0;
      double closestGreen = greenLevels[0];
      double minDistance = MathAbs(currentPrice - closestGreen);

      for(int i = 1; i < greenCount; i++)
      {
         double distance = MathAbs(currentPrice - greenLevels[i]);
         if(distance < minDistance)
         {
            minDistance = distance;
            closestGreen = greenLevels[i];
         }
      }

      info += "أقرب مستوى أخضر: " + DoubleToString(closestGreen, Digits) +
              " (المسافة: " + DoubleToString(minDistance / Point, 1) + " نقطة)\n";
   }

   // عرض حالة الإشارات
   info += "إشارة الشراء: " + (lastBuySignal ? "نشطة" : "غير نشطة") + "\n";
   info += "إشارة البيع: " + (lastSellSignal ? "نشطة" : "غير نشطة") + "\n";

   Comment(info);
}

//+------------------------------------------------------------------+
//| عد الصفقات المفتوحة                                              |
//+------------------------------------------------------------------+
int CountOpenTrades()
{
   int count = 0;
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS) && OrderSymbol() == Symbol() && 
         OrderMagicNumber() == MagicNumber)
      {
         count++;
      }
   }
   return count;
}

//+------------------------------------------------------------------+
//| حساب إجمالي الربح                                               |
//+------------------------------------------------------------------+
double GetTotalProfit()
{
   double profit = 0;
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS) && OrderSymbol() == Symbol() &&
         OrderMagicNumber() == MagicNumber)
      {
         profit += OrderProfit() + OrderSwap() + OrderCommission();
      }
   }
   return profit;
}

//+------------------------------------------------------------------+
//| البحث المتقدم عن مستويات فيبوناتشي بألوان محددة                  |
//+------------------------------------------------------------------+
bool FindFibonacciLevelsByColor(color targetColor, double &levelPrices[], int &count)
{
   count = 0;
   int total = ObjectsTotal();

   for(int i = total-1; i >= 0; i--)
   {
      string name = ObjectName(i);

      // البحث عن كائنات فيبوناتشي المختلفة
      if(StringFind(name, "Fibo") >= 0 || StringFind(name, "fibo") >= 0 ||
         StringFind(name, "MyFibo") >= 0 || ObjectType(name) == OBJ_FIBO ||
         StringFind(name, "Fibonacci") >= 0 || StringFind(name, "fibonacci") >= 0)
      {
         // التحقق من لون الكائن
         if(ObjectGet(name, OBJPROP_COLOR) == targetColor)
         {
            double price1 = ObjectGet(name, OBJPROP_PRICE1);
            double price2 = ObjectGet(name, OBJPROP_PRICE2);

            // حساب مستوى 61.8%
            double levelPrice = price1 + (price2 - price1) * FiboLevel;

            // إضافة المستوى إلى المصفوفة
            if(count < ArraySize(levelPrices))
            {
               levelPrices[count] = levelPrice;
               count++;
            }
         }
      }
   }

   return (count > 0);
}

//+------------------------------------------------------------------+
//| التحقق من ملامسة السعر لأي من مستويات فيبوناتشي                  |
//+------------------------------------------------------------------+
bool CheckPriceTouchesAnyLevel(double levels[], int count, bool isBuySignal)
{
   double currentPrice = isBuySignal ? Ask : Bid;
   double tolerance = FiboTolerance * Point;

   for(int i = 0; i < count; i++)
   {
      double levelPrice = levels[i];

      if(isBuySignal)
      {
         // إشارة شراء: السعر يلامس المستوى من الأسفل
         if(currentPrice >= levelPrice - tolerance &&
            currentPrice <= levelPrice + tolerance &&
            Low[1] < levelPrice - tolerance)
         {
            Print("تم اكتشاف ملامسة مستوى فيبوناتشي للشراء عند: ", DoubleToString(levelPrice, Digits));
            return true;
         }
      }
      else
      {
         // إشارة بيع: السعر يلامس المستوى من الأعلى
         if(currentPrice >= levelPrice - tolerance &&
            currentPrice <= levelPrice + tolerance &&
            High[1] > levelPrice + tolerance)
         {
            Print("تم اكتشاف ملامسة مستوى فيبوناتشي للبيع عند: ", DoubleToString(levelPrice, Digits));
            return true;
         }
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| التحقق من وجود صفقات مفتوحة من نفس النوع                        |
//+------------------------------------------------------------------+
bool HasOpenTradesOfType(int orderType)
{
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS) && OrderSymbol() == Symbol() &&
         OrderMagicNumber() == MagicNumber && OrderType() == orderType)
      {
         return true;
      }
   }
   return false;
}

//+------------------------------------------------------------------+
//| إغلاق جميع الصفقات من نوع معين                                  |
//+------------------------------------------------------------------+
void CloseTradesOfType(int orderType)
{
   for(int i = OrdersTotal()-1; i >= 0; i--)
   {
      if(OrderSelect(i, SELECT_BY_POS) && OrderSymbol() == Symbol() &&
         OrderMagicNumber() == MagicNumber && OrderType() == orderType)
      {
         bool result = false;
         if(orderType == OP_BUY)
            result = OrderClose(OrderTicket(), OrderLots(), Bid, Slippage, clrRed);
         else if(orderType == OP_SELL)
            result = OrderClose(OrderTicket(), OrderLots(), Ask, Slippage, clrBlue);

         if(result)
            Print("تم إغلاق الصفقة رقم: ", OrderTicket());
         else
            Print("فشل في إغلاق الصفقة رقم: ", OrderTicket(), " - الخطأ: ", GetLastError());
      }
   }
}

//+------------------------------------------------------------------+
//| تحديث دالة التحقق من إشارة الشراء                               |
//+------------------------------------------------------------------+
bool CheckBuySignalAdvanced()
{
   double redLevels[10];
   int redCount = 0;

   // البحث عن مستويات فيبوناتشي الحمراء
   if(FindFibonacciLevelsByColor(RedFiboColor, redLevels, redCount))
   {
      // التحقق من ملامسة السعر لأي من المستويات
      return CheckPriceTouchesAnyLevel(redLevels, redCount, true);
   }

   return false;
}

//+------------------------------------------------------------------+
//| تحديث دالة التحقق من إشارة البيع                                |
//+------------------------------------------------------------------+
bool CheckSellSignalAdvanced()
{
   double greenLevels[10];
   int greenCount = 0;

   // البحث عن مستويات فيبوناتشي الخضراء
   if(FindFibonacciLevelsByColor(GreenFiboColor, greenLevels, greenCount))
   {
      // التحقق من ملامسة السعر لأي من المستويات
      return CheckPriceTouchesAnyLevel(greenLevels, greenCount, false);
   }

   return false;
}
