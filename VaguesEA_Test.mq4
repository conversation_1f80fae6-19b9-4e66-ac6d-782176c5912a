//+------------------------------------------------------------------+
//|                                              VaguesEA_Test.mq4  |
//|                        إكسبرت اختبار مبسط لمؤشر Vagues           |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Alsayaf EA Test"
#property link      ""
#property version   "1.00"
#property strict

//--- Input parameters
input group "=== إعدادات الاختبار ==="
input bool TestMode = true;               // وضع الاختبار
input color RedFiboColor = clrRed;        // لون فيبوناتشي الأحمر
input color GreenFiboColor = clrLime;     // لون فيبوناتشي الأخضر

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("=== بدء اختبار إكسبرت Vagues EA ===");
   
   if(TestMode)
   {
      Print("وضع الاختبار مفعل - سيتم طباعة معلومات تشخيصية فقط");
      TestFibonacciDetection();
   }
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Print("=== انتهاء اختبار إكسبرت Vagues EA ===");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   if(TestMode)
   {
      static datetime lastTest = 0;
      if(TimeCurrent() - lastTest > 30) // كل 30 ثانية
      {
         TestFibonacciDetection();
         lastTest = TimeCurrent();
      }
   }
}

//+------------------------------------------------------------------+
//| اختبار كشف مستويات فيبوناتشي                                    |
//+------------------------------------------------------------------+
void TestFibonacciDetection()
{
   Print("--- اختبار كشف مستويات فيبوناتشي ---");
   
   int total = ObjectsTotal();
   Print("إجمالي الكائنات على الشارت: ", total);
   
   int fiboCount = 0;
   int redFiboCount = 0;
   int greenFiboCount = 0;
   
   for(int i = 0; i < total; i++)
   {
      string name = ObjectName(i);
      int objType = ObjectType(name);
      color objColor = (color)ObjectGet(name, OBJPROP_COLOR);
      
      // فحص كائنات فيبوناتشي
      bool isFibo = false;
      if(StringFind(name, "MyFibo") >= 0 || StringFind(name, "Target") >= 0 ||
         StringFind(name, "Fibo") >= 0 || StringFind(name, "fibo") >= 0 || 
         objType == OBJ_FIBO || objType == OBJ_FIBOFAN || 
         objType == OBJ_FIBOARC || objType == OBJ_FIBOCHANNEL)
      {
         isFibo = true;
         fiboCount++;
         
         Print("كائن فيبوناتشي: ", name, " | النوع: ", objType, " | اللون: ", objColor);
         
         if(objColor == RedFiboColor)
         {
            redFiboCount++;
            Print("  -> مستوى فيبوناتشي أحمر مكتشف!");
         }
         else if(objColor == GreenFiboColor)
         {
            greenFiboCount++;
            Print("  -> مستوى فيبوناتشي أخضر مكتشف!");
         }
         
         // حساب مستوى 61.8%
         double price1 = ObjectGet(name, OBJPROP_PRICE1);
         double price2 = ObjectGet(name, OBJPROP_PRICE2);
         
         if(price1 > 0 && price2 > 0 && price1 != price2)
         {
            double level618 = price1 + (price2 - price1) * 0.618;
            double currentPrice = (Bid + Ask) / 2.0;
            double distance = MathAbs(currentPrice - level618) / Point;
            
            Print("  -> مستوى 61.8%: ", DoubleToString(level618, Digits));
            Print("  -> المسافة من السعر الحالي: ", DoubleToString(distance, 1), " نقطة");
         }
      }
   }
   
   Print("ملخص النتائج:");
   Print("- إجمالي كائنات فيبوناتشي: ", fiboCount);
   Print("- مستويات فيبوناتشي حمراء: ", redFiboCount);
   Print("- مستويات فيبوناتشي خضراء: ", greenFiboCount);
   Print("- السعر الحالي: Ask=", DoubleToString(Ask, Digits), " | Bid=", DoubleToString(Bid, Digits));
   
   if(fiboCount == 0)
   {
      Print("تحذير: لم يتم العثور على أي كائنات فيبوناتشي!");
      Print("تأكد من أن مؤشر Vagues يعمل ويرسم مستويات فيبوناتشي");
   }
   
   Print("--- انتهاء الاختبار ---");
}

//+------------------------------------------------------------------+
//| اختبار ملامسة مستوى فيبوناتشي                                   |
//+------------------------------------------------------------------+
bool TestLevelTouch(double levelPrice, double tolerance)
{
   double currentPrice = (Bid + Ask) / 2.0;
   double tolerancePoints = tolerance * Point;
   
   if(currentPrice >= levelPrice - tolerancePoints && 
      currentPrice <= levelPrice + tolerancePoints)
   {
      Print("ملامسة مكتشفة! المستوى: ", DoubleToString(levelPrice, Digits), 
            " | السعر: ", DoubleToString(currentPrice, Digits));
      return true;
   }
   
   return false;
}
