//+------------------------------------------------------------------+
//|                                                 SquareOf9_EA.mq4 |
//|                        إكسبرت Square of 9                       |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Alsayaf EA"
#property link      ""
#property version   "2.00"
#property strict

//--- ENUM for Square of 9 Mode
enum ENUM_SQUAREOF9_MODE { S9_Close, S9_Open };
input ENUM_SQUAREOF9_MODE SquareOf9Mode = S9_Close; // طريقة الحساب: إغلاق أو افتتاح

//--- إعدادات المؤشر الخارجي
input string ExternalIndicatorName = ""; // اسم المؤشر الخارجي (اتركه فارغاً إذا لم تستخدم)
input int BuyBuffer = 0;  // رقم بفر الشراء في المؤشر الخارجي
input int SellBuffer = 1; // رقم بفر البيع في المؤشر الخارجي

//--- Input parameters
input group "=== إعدادات Square of 9 ==="
input int TargetHour1 = -1;
input int TargetHour2 = -1;
input int TargetHour3 = -1;
input string TargetHour4 = "";
input string TargetHour5 = "";
input string TargetHour6 = ""; // الساعات المحددة (اتركها فارغة للساعات غير المستخدمة)
input bool UseCurrentDayOnly = true;   // استخدام اليوم الحالي فقط
input double SquareStep = 1.0;         // خطوة Square of 9

input group "=== إعدادات الصفقات ==="
input double LotSize = 0.1;            // حجم اللوت
input int MagicNumber = 54321;         // الرقم السحري
input int MaxTradesPerLevel = 1;       // عدد الصفقات لكل مستوى

input group "=== إدارة المخاطر ==="
input double TakeProfit = 30.0;        // أخذ الربح (بالنقاط)
input double StopLoss = 50.0;          // إيقاف الخسارة (بالنقاط)
input double BreakEven = 20.0;         // البريك إيفن (بالنقاط)
input double TrailingStart = 800.0;    // بدء التريلينج (بالنقاط)
input double TrailingStop = 500.0;     // قيمة التريلينج (بالنقاط)

input group "=== إعدادات إضافية ==="
input bool AllowNewTrades = true;      // السماح بصفقات جديدة
input bool ShowInfo = true;            // إظهار المعلومات على الشارت
input bool ShowLevels = true;          // إظهار المستويات على الشارت

//--- Struct to hold level info for each hour
struct HourLevelInfo {
   int hour;
   double closePrice;
   double buyLevel;
   double sellLevel;
   bool levelsCalculated;
   bool buyTradeOpened;
   bool sellTradeOpened;
   datetime targetTime;
   datetime nextTargetTime;
   bool waitingForClose;
};

HourLevelInfo hourLevels[6];
int hourLevelsCount = 0; // عدد الساعات الفريدة
int TargetHours[6]; // مصفوفة داخلية للساعات
int activeHourIndex = -1; // مؤشر الساعة النشطة

// دالة حذف خطوط المستويات من الشارت
void RemoveLevelLines()
{
   ObjectDelete("BuyLevel");
   ObjectDelete("SellLevel");
   ObjectDelete("TargetPrice");
}

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // دعم الساعات الفارغة
   int inputHours[6];
   inputHours[0] = TargetHour1;
   inputHours[1] = TargetHour2;
   inputHours[2] = TargetHour3;
   inputHours[3] = (StringLen(TargetHour4) > 0) ? StrToInteger(TargetHour4) : -1;
   inputHours[4] = (StringLen(TargetHour5) > 0) ? StrToInteger(TargetHour5) : -1;
   inputHours[5] = (StringLen(TargetHour6) > 0) ? StrToInteger(TargetHour6) : -1;

   // بناء مصفوفة الساعات الفريدة فقط
   hourLevelsCount = 0;
   for(int i=0; i<6; i++) {
      int h = inputHours[i];
      if(h < 0 || h > 23) continue;
      bool found = false;
      for(int j=0; j<hourLevelsCount; j++) {
         if(hourLevels[j].hour == h) { found = true; break; }
      }
      if(!found) {
         hourLevels[hourLevelsCount].hour = h;
         hourLevels[hourLevelsCount].closePrice = 0.0;
         hourLevels[hourLevelsCount].buyLevel = 0.0;
         hourLevels[hourLevelsCount].sellLevel = 0.0;
         hourLevels[hourLevelsCount].levelsCalculated = false;
         hourLevels[hourLevelsCount].buyTradeOpened = false;
         hourLevels[hourLevelsCount].sellTradeOpened = false;
         hourLevels[hourLevelsCount].targetTime = 0;
         hourLevels[hourLevelsCount].nextTargetTime = 0;
         hourLevels[hourLevelsCount].waitingForClose = false;
         hourLevelsCount++;
      }
   }
   for(int i=0; i<hourLevelsCount; i++) {
      CalculateNextTargetTime(i);
   }
   Print("تم تهيئة إكسبرت Square of 9 بنجاح");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // عند حذف الإكسبرت فقط يتم حذف جميع الخطوط
   RemoveLevelLines();
   Print("تم إيقاف إكسبرت Square of 9");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   int nextActive = -1;
   datetime soonest = 0;

   for(int i=0; i<hourLevelsCount; i++) {
      if(hourLevels[i].hour < 0 || hourLevels[i].hour > 23) continue;
      CheckForTargetHourClose(i);

      if(hourLevels[i].levelsCalculated && !hourLevels[i].buyTradeOpened && !hourLevels[i].sellTradeOpened) {
         if(nextActive == -1 || hourLevels[i].targetTime > hourLevels[nextActive].targetTime) {
            nextActive = i;
         }
      }
      if(!hourLevels[i].levelsCalculated && hourLevels[i].waitingForClose) {
         if(soonest == 0 || hourLevels[i].nextTargetTime < soonest) {
            soonest = hourLevels[i].nextTargetTime;
         }
      }
   }

   if(nextActive != activeHourIndex) {
      if(activeHourIndex != -1 && hourLevels[activeHourIndex].levelsCalculated &&
         !hourLevels[activeHourIndex].buyTradeOpened && !hourLevels[activeHourIndex].sellTradeOpened) {
         hourLevels[activeHourIndex].levelsCalculated = false;
         hourLevels[activeHourIndex].closePrice = 0.0;
         hourLevels[activeHourIndex].buyLevel = 0.0;
         hourLevels[activeHourIndex].sellLevel = 0.0;
         hourLevels[activeHourIndex].buyTradeOpened = false;
         hourLevels[activeHourIndex].sellTradeOpened = false;
      }
      activeHourIndex = nextActive;
   }

   if(activeHourIndex != -1 && hourLevels[activeHourIndex].levelsCalculated) {
      CheckTradingSignals(activeHourIndex);
   }

   ManageTrades();

   if(ShowInfo) ShowChartInfo();

   if(ShowLevels && activeHourIndex != -1 && hourLevels[activeHourIndex].levelsCalculated)
      DrawLevelsOnChart(activeHourIndex);
}

//+------------------------------------------------------------------+
//| حساب وقت الإغلاق القادم للساعة المحددة                           |
//+------------------------------------------------------------------+
void CalculateNextTargetTime(int idx)
{
   datetime currentTime = TimeCurrent();
   MqlDateTime timeStruct;
   TimeToStruct(currentTime, timeStruct);

   // بداية الساعة المحددة
   timeStruct.hour = hourLevels[idx].hour;
   timeStruct.min = 0;
   timeStruct.sec = 0;
   datetime startHour = StructToTime(timeStruct);

   // نهاية الامتداد (بداية الساعة التالية)
   datetime endHour = startHour + 3600;

   if(currentTime < endHour)
      hourLevels[idx].nextTargetTime = endHour;
   else
      hourLevels[idx].nextTargetTime = endHour + 86400;

   hourLevels[idx].waitingForClose = true;
}

//+------------------------------------------------------------------+
//| احصل على سعر إغلاق أو افتتاح الساعة المحددة                     |
//+------------------------------------------------------------------+
double GetCurrentTargetHourPrice(int hour)
{
   datetime currentTime = TimeCurrent();
   MqlDateTime timeStruct;
   TimeToStruct(currentTime, timeStruct);

   // بداية الشمعة للساعة المحددة
   timeStruct.hour = hour;
   timeStruct.min = 0;
   timeStruct.sec = 0;
   datetime barOpenTime = StructToTime(timeStruct);

   // نبحث عن الشمعة التي تبدأ عند barOpenTime
   int targetBar = iBarShift(Symbol(), PERIOD_H1, barOpenTime, false);

   if(targetBar < 0)
   {
      Print("لم يتم العثور على بيانات للساعة ", hour);
      return 0.0;
   }

   double price = 0.0;
   if(SquareOf9Mode == S9_Open)
   {
      price = iOpen(Symbol(), PERIOD_H1, targetBar);
      Print("تم العثور على سعر الافتتاح للساعة ", hour, ":00 - السعر: ", DoubleToString(price, Digits));
   }
   else // S9_Close
   {
      price = iClose(Symbol(), PERIOD_H1, targetBar);
      Print("تم العثور على سعر الإغلاق للساعة ", hour, ":00 - السعر: ", DoubleToString(price, Digits));
   }

   if(StringFind(Symbol(), "XAU") >= 0 || StringFind(Symbol(), "GOLD") >= 0)
      price = MathFloor(price);

   return price;
}

//+------------------------------------------------------------------+
//| تحقق من إغلاق الساعة المحددة                                     |
//+------------------------------------------------------------------+
void CheckForTargetHourClose(int idx)
{
   datetime currentTime = TimeCurrent();

   if(hourLevels[idx].waitingForClose && currentTime >= hourLevels[idx].nextTargetTime)
   {
      // نأخذ سعر الشمعة (إغلاق أو افتتاح) حسب الخيار
      hourLevels[idx].closePrice = GetCurrentTargetHourPrice(hourLevels[idx].hour);
      if(hourLevels[idx].closePrice > 0)
      {
         CalculateSquareOf9LevelsFromPrice(idx);
         hourLevels[idx].targetTime = hourLevels[idx].nextTargetTime - 3600;
         hourLevels[idx].nextTargetTime += 86400;
         hourLevels[idx].waitingForClose = true;
         Print("تم الحصول على السعر الجديد للساعة ", hourLevels[idx].hour, ": ", DoubleToString(hourLevels[idx].closePrice, Digits));
      }
   }
}

//+------------------------------------------------------------------+
//| حساب مستويات Square of 9 من السعر                                |
//+------------------------------------------------------------------+
void CalculateSquareOf9LevelsFromPrice(int idx)
{
   // عند حساب مستويات جديدة، احذف الخطوط القديمة أولاً
   RemoveLevelLines();
   double price = hourLevels[idx].closePrice;
   if(price <= 0) return;

   double squareRoot = MathSqrt(price);
   double increment = squareRoot * 0.25;

   hourLevels[idx].buyLevel = price + increment;
   hourLevels[idx].sellLevel = price - increment;
   hourLevels[idx].levelsCalculated = true;
   hourLevels[idx].buyTradeOpened = false;
   hourLevels[idx].sellTradeOpened = false;

   Print("تم حساب مستويات Square of 9 للساعة ", hourLevels[idx].hour, ":00");
   Print("سعر الأساس المرجعي: ", DoubleToString(price, Digits));
   Print("مستوى الشراء: ", DoubleToString(hourLevels[idx].buyLevel, Digits));
   Print("مستوى البيع: ", DoubleToString(hourLevels[idx].sellLevel, Digits));
   Print("الجذر التربيعي: ", DoubleToString(squareRoot, 4));
   Print("الزيادة المحسوبة: ", DoubleToString(increment, 4));
}

//+------------------------------------------------------------------+
//| إشارات التداول للساعة النشطة فقط                                 |
//+------------------------------------------------------------------+
void CheckTradingSignals(int idx)
{
   if(!AllowNewTrades || !hourLevels[idx].levelsCalculated)
      return;

   double currentPrice = (Bid + Ask) / 2.0;

   if(!hourLevels[idx].buyTradeOpened && currentPrice >= hourLevels[idx].buyLevel)
   {
      if(CountTradesAtLevel(OP_BUY) < MaxTradesPerLevel)
      {
         OpenBuyTrade();
         hourLevels[idx].buyTradeOpened = true;
      }
   }

   if(!hourLevels[idx].sellTradeOpened && currentPrice <= hourLevels[idx].sellLevel)
   {
      if(CountTradesAtLevel(OP_SELL) < MaxTradesPerLevel)
      {
         OpenSellTrade();
         hourLevels[idx].sellTradeOpened = true;
      }
   }
}

//+------------------------------------------------------------------+
//| Open Buy Trade                                                   |
//+------------------------------------------------------------------+
void OpenBuyTrade()
{
   if(activeHourIndex == -1) return;
   double buyLevel = hourLevels[activeHourIndex].buyLevel;
   int ticket = OrderSend(Symbol(), OP_BUY, LotSize, Ask, 3, 0, 0,
                         "Square of 9 Buy at " + DoubleToString(buyLevel, Digits),
                         MagicNumber, 0, clrBlue);
   if(ticket > 0)
   {
      Print("تم فتح صفقة شراء - التذكرة: ", ticket, " عند المستوى: ", DoubleToString(buyLevel, Digits));
   }
   else
   {
      Print("فشل في فتح صفقة الشراء - الخطأ: ", GetLastError());
   }
}

//+------------------------------------------------------------------+
//| Open Sell Trade                                                  |
//+------------------------------------------------------------------+
void OpenSellTrade()
{
   if(activeHourIndex == -1) return;
   double sellLevel = hourLevels[activeHourIndex].sellLevel;
   int ticket = OrderSend(Symbol(), OP_SELL, LotSize, Bid, 3, 0, 0,
                         "Square of 9 Sell at " + DoubleToString(sellLevel, Digits),
                         MagicNumber, 0, clrRed);
   if(ticket > 0)
   {
      Print("تم فتح صفقة بيع - التذكرة: ", ticket, " عند المستوى: ", DoubleToString(sellLevel, Digits));
   }
   else
   {
      Print("فشل في فتح صفقة البيع - الخطأ: ", GetLastError());
   }
}

//+------------------------------------------------------------------+
//| Count trades at specific level                                   |
//+------------------------------------------------------------------+
int CountTradesAtLevel(int orderType)
{
   int count = 0;
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber && OrderType() == orderType)
         {
            count++;
         }
      }
   }
   return count;
}

//+------------------------------------------------------------------+
//| Manage existing trades                                           |
//+------------------------------------------------------------------+
void ManageTrades()
{
   double indBuy0 = 0, indSell0 = 0;
   bool useIndicator = (StringLen(ExternalIndicatorName) > 0);
   if(useIndicator)
   {
      indBuy0 = iCustom(Symbol(), 0, ExternalIndicatorName, 0, BuyBuffer, 0);
      indSell0 = iCustom(Symbol(), 0, ExternalIndicatorName, 0, SellBuffer, 0);
   }
   for(int i = OrdersTotal() - 1; i >= 0; i--)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber)
         {
            double currentPrice;
            double openPrice = OrderOpenPrice();
            double profit;
            int ticket = OrderTicket();
            if(OrderType() == OP_BUY)
            {
               currentPrice = Bid;
               profit = (currentPrice - openPrice) / Point;
               //--- إغلاق حسب المؤشر الخارجي عند وجود إشارة بيع
               if(useIndicator && indSell0 > 0)
               {
                  Print("[SquareOf9] سيتم إغلاق صفقة الشراء بسبب ظهور إشارة بيع من المؤشر الخارجي indSell0=", indSell0);
                  CloseOrder(ticket, "إشارة بيع من المؤشر الخارجي");
                  continue;
               }
               //--- Check Take Profit
               if(TakeProfit > 0 && profit >= TakeProfit)
               {
                  CloseOrder(ticket, "Take Profit reached");
                  continue;
               }
               //--- Check Stop Loss
               if(StopLoss > 0 && profit <= -StopLoss)
               {
                  CloseOrder(ticket, "Stop Loss hit");
                  continue;
               }
               //--- Check Break Even
               if(BreakEven > 0 && profit >= BreakEven && OrderStopLoss() < openPrice)
               {
                  ModifyOrder(ticket, openPrice, OrderTakeProfit());
               }
               //--- Trailing Stop
               if(TrailingStart > 0 && TrailingStop > 0 && profit >= TrailingStart)
               {
                  double newSL = openPrice + TrailingStop * Point;
                  if(OrderStopLoss() < newSL)
                     ModifyOrder(ticket, newSL, OrderTakeProfit());
               }
            }
            else if(OrderType() == OP_SELL)
            {
               currentPrice = Ask;
               profit = (openPrice - currentPrice) / Point;
               //--- إغلاق حسب المؤشر الخارجي عند وجود إشارة شراء
               if(useIndicator && indBuy0 > 0)
               {
                  Print("[SquareOf9] سيتم إغلاق صفقة البيع بسبب ظهور إشارة شراء من المؤشر الخارجي indBuy0=", indBuy0);
                  CloseOrder(ticket, "إشارة شراء من المؤشر الخارجي");
                  continue;
               }
               //--- Check Take Profit
               if(TakeProfit > 0 && profit >= TakeProfit)
               {
                  CloseOrder(ticket, "Take Profit reached");
                  continue;
               }
               //--- Check Stop Loss
               if(StopLoss > 0 && profit <= -StopLoss)
               {
                  CloseOrder(ticket, "Stop Loss hit");
                  continue;
               }
               //--- Check Break Even
               if(BreakEven > 0 && profit >= BreakEven && (OrderStopLoss() > openPrice || OrderStopLoss() == 0))
               {
                  ModifyOrder(ticket, openPrice, OrderTakeProfit());
               }
               //--- Trailing Stop
               if(TrailingStart > 0 && TrailingStop > 0 && profit >= TrailingStart)
               {
                  double newSL = openPrice - TrailingStop * Point;
                  if(OrderStopLoss() == 0 || OrderStopLoss() > newSL)
                     ModifyOrder(ticket, newSL, OrderTakeProfit());
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Modify Order                                                     |
//+------------------------------------------------------------------+
void ModifyOrder(int ticket, double stopLoss, double takeProfit)
{
   if(OrderSelect(ticket, SELECT_BY_TICKET))
   {
      bool result = OrderModify(ticket, OrderOpenPrice(), stopLoss, takeProfit, 0, clrGreen);

      if(result)
      {
         Print("تم تعديل الصفقة ", ticket, " - البريك إيفن مفعل");
      }
      else
      {
         Print("فشل في تعديل الصفقة ", ticket, " - الخطأ: ", GetLastError());
      }
   }
}

//+------------------------------------------------------------------+
//| Draw levels on chart                                             |
//+------------------------------------------------------------------+
void DrawLevelsOnChart(int idx)
{
   if(idx < 0 || !hourLevels[idx].levelsCalculated)
      return;

   // BuyLevel
   if(ObjectFind("BuyLevel") < 0)
   {
      ObjectCreate("BuyLevel", OBJ_HLINE, 0, 0, hourLevels[idx].buyLevel);
      ObjectSet("BuyLevel", OBJPROP_COLOR, clrBlue);
      ObjectSet("BuyLevel", OBJPROP_STYLE, STYLE_SOLID);
      ObjectSet("BuyLevel", OBJPROP_WIDTH, 2);
      ObjectSetText("BuyLevel", "مستوى الشراء: " + DoubleToString(hourLevels[idx].buyLevel, Digits));
      ObjectSet("BuyLevel", OBJPROP_BACK, true);
      ObjectSet("BuyLevel", OBJPROP_RAY, false);
   }
   else
   {
      ObjectSet("BuyLevel", OBJPROP_PRICE1, hourLevels[idx].buyLevel);
      ObjectSetText("BuyLevel", "مستوى الشراء: " + DoubleToString(hourLevels[idx].buyLevel, Digits));
      ObjectSet("BuyLevel", OBJPROP_BACK, true);
      ObjectSet("BuyLevel", OBJPROP_RAY, false);
   }

   // SellLevel
   if(ObjectFind("SellLevel") < 0)
   {
      ObjectCreate("SellLevel", OBJ_HLINE, 0, 0, hourLevels[idx].sellLevel);
      ObjectSet("SellLevel", OBJPROP_COLOR, clrRed);
      ObjectSet("SellLevel", OBJPROP_STYLE, STYLE_SOLID);
      ObjectSet("SellLevel", OBJPROP_WIDTH, 2);
      ObjectSetText("SellLevel", "مستوى البيع: " + DoubleToString(hourLevels[idx].sellLevel, Digits));
      ObjectSet("SellLevel", OBJPROP_BACK, true);
      ObjectSet("SellLevel", OBJPROP_RAY, false);
   }
   else
   {
      ObjectSet("SellLevel", OBJPROP_PRICE1, hourLevels[idx].sellLevel);
      ObjectSetText("SellLevel", "مستوى البيع: " + DoubleToString(hourLevels[idx].sellLevel, Digits));
      ObjectSet("SellLevel", OBJPROP_BACK, true);
      ObjectSet("SellLevel", OBJPROP_RAY, false);
   }

   // TargetPrice
   if(ObjectFind("TargetPrice") < 0)
   {
      ObjectCreate("TargetPrice", OBJ_HLINE, 0, 0, hourLevels[idx].closePrice);
      ObjectSet("TargetPrice", OBJPROP_COLOR, clrYellow);
      ObjectSet("TargetPrice", OBJPROP_STYLE, STYLE_DOT);
      ObjectSet("TargetPrice", OBJPROP_WIDTH, 1);
      ObjectSetText("TargetPrice", "سعر الأساس المرجعي: " + DoubleToString(hourLevels[idx].closePrice, Digits));
      ObjectSet("TargetPrice", OBJPROP_BACK, true);
      ObjectSet("TargetPrice", OBJPROP_RAY, false);
   }
   else
   {
      ObjectSet("TargetPrice", OBJPROP_PRICE1, hourLevels[idx].closePrice);
      ObjectSetText("TargetPrice", "سعر الأساس المرجعي: " + DoubleToString(hourLevels[idx].closePrice, Digits));
      ObjectSet("TargetPrice", OBJPROP_BACK, true);
      ObjectSet("TargetPrice", OBJPROP_RAY, false);
   }
}

//+------------------------------------------------------------------+
//| Show information on chart                                        |
//+------------------------------------------------------------------+
void ShowChartInfo()
{
   string info = "";
   info += "=== إكسبرت Square of 9 ===\n";
   info += "الساعات المحددة: ";
   for(int i=0; i<hourLevelsCount; i++) {
      if(hourLevels[i].hour >= 0 && hourLevels[i].hour <= 23)
         info += IntegerToString(hourLevels[i].hour) + " ";
   }
   info += "\n";

   if(activeHourIndex != -1) {
      info += "الساعة النشطة: " + IntegerToString(hourLevels[activeHourIndex].hour) + ":00\n";
      if(hourLevels[activeHourIndex].waitingForClose)
      {
         info += "الحالة: في انتظار الإغلاق القادم\n";
         info += "الوقت المحدد القادم: " + TimeToString(hourLevels[activeHourIndex].nextTargetTime, TIME_DATE|TIME_MINUTES) + "\n";
         datetime currentTime = TimeCurrent();
         int remainingSeconds = (int)(hourLevels[activeHourIndex].nextTargetTime - currentTime);
         int remainingHours = remainingSeconds / 3600;
         int remainingMins = (remainingSeconds % 3600) / 60;
         info += "الوقت المتبقي: " + IntegerToString(remainingHours) + ":" +
                 StringFormat("%02d", remainingMins) + " ساعة\n";
      }

      if(hourLevels[activeHourIndex].levelsCalculated)
      {
         info += "سعر الأساس المرجعي: " + DoubleToString(hourLevels[activeHourIndex].closePrice, Digits) + "\n";
         info += "مستوى الشراء: " + DoubleToString(hourLevels[activeHourIndex].buyLevel, Digits);
         if(hourLevels[activeHourIndex].buyTradeOpened)
            info += " (تم التنفيذ)";
         else
            info += " (في الانتظار)";
         info += "\n";

         info += "مستوى البيع: " + DoubleToString(hourLevels[activeHourIndex].sellLevel, Digits);
         if(hourLevels[activeHourIndex].sellTradeOpened)
            info += " (تم التنفيذ)";
         else
            info += " (في الانتظار)";
         info += "\n";

         double currentPrice = (Bid + Ask) / 2.0;
         double distanceToBuy = (hourLevels[activeHourIndex].buyLevel - currentPrice) / Point;
         double distanceToSell = (currentPrice - hourLevels[activeHourIndex].sellLevel) / Point;

         info += "المسافة لمستوى الشراء: " + DoubleToString(distanceToBuy, 1) + " نقطة\n";
         info += "المسافة لمستوى البيع: " + DoubleToString(distanceToSell, 1) + " نقطة\n";
      }
      else if(hourLevels[activeHourIndex].waitingForClose)
      {
         info += "في انتظار حساب المستويات...\n";
      }
   }

   info += "السعر الحالي: " + DoubleToString((Bid + Ask) / 2.0, Digits) + "\n";
   info += "عدد الصفقات المفتوحة: " + IntegerToString(CountOpenTrades()) + "\n";
   info += "إجمالي الربح: " + DoubleToString(GetTotalProfit(), 2) + "\n";

   Comment(info);
}

//+------------------------------------------------------------------+
//| Count open trades                                                |
//+------------------------------------------------------------------+
int CountOpenTrades()
{
   int count = 0;
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber)
         {
            count++;
         }
      }
   }
   return count;
}

//+------------------------------------------------------------------+
//| Get total profit                                                 |
//+------------------------------------------------------------------+
double GetTotalProfit()
{
   double totalProfit = 0.0;
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber)
         {
            totalProfit += OrderProfit() + OrderSwap() + OrderCommission();
         }
      }
   }
   return totalProfit;
}

//+------------------------------------------------------------------+
//| Reset trade flags for new levels                                 |
//+------------------------------------------------------------------+
void ResetTradeFlags()
{
   if(activeHourIndex == -1) return;
   hourLevels[activeHourIndex].buyTradeOpened = false;
   hourLevels[activeHourIndex].sellTradeOpened = false;
   Print("تم إعادة تعيين حالة الصفقات - يمكن فتح صفقات جديدة عند الوصول للمستويات");
}

//+------------------------------------------------------------------+
//| Close all trades                                                 |
//+------------------------------------------------------------------+
void CloseAllTrades()
{
   for(int i = OrdersTotal() - 1; i >= 0; i--)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber)
         {
            double closePrice;
            if(OrderType() == OP_BUY)
               closePrice = Bid;
            else
               closePrice = Ask;

            bool result = OrderClose(OrderTicket(), OrderLots(), closePrice, 3, clrYellow);

            if(result)
            {
               Print("تم إغلاق الصفقة ", OrderTicket());
            }
            else
            {
               Print("فشل في إغلاق الصفقة ", OrderTicket(), " - الخطأ: ", GetLastError());
            }
         }
      }
   }
}

// دالة إغلاق الصفقة برمجياً
void CloseOrder(int ticket, string reason)
{
   if(OrderSelect(ticket, SELECT_BY_TICKET))
   {
      double closePrice;
      if(OrderType() == OP_BUY)
         closePrice = Bid;
      else
         closePrice = Ask;
      bool result = OrderClose(ticket, OrderLots(), closePrice, 3, clrYellow);
      if(result)
      {
         Print("تم إغلاق الصفقة ", ticket, " - السبب: ", reason);
      }
      else
      {
         Print("فشل في إغلاق الصفقة ", ticket, " - الخطأ: ", GetLastError());
      }
   }
}

//+------------------------------------------------------------------+
//| Test Square of 9 calculation with example                        |
//+------------------------------------------------------------------+
void TestSquareOf9Calculation()
{
   Print("=== اختبار حساب Square of 9 ===");

   // Test with all provided examples
   double testPrices[3] = {3382.0, 3312.0, 3254.0};
   double expectedBuy[3] = {3396.54, 3326.39, 3268.26};
   double expectedSell[3] = {3367.46, 3297.61, 3239.74};

   for(int i = 0; i < 3; i++)
   {
      double testPrice = testPrices[i];
      double squareRoot = MathSqrt(testPrice);
      double increment = squareRoot * 0.25;

      double calculatedBuy = testPrice + increment;
      double calculatedSell = testPrice - increment;

      Print("--- مثال ", (i+1), " ---");
      Print("السعر المرجعي: ", DoubleToString(testPrice, 0));
      Print("الجذر التربيعي: ", DoubleToString(squareRoot, 4));
      Print("الزيادة المحسوبة: ", DoubleToString(increment, 4));
      Print("مستوى الشراء المحسوب: ", DoubleToString(calculatedBuy, 2));
      Print("مستوى الشراء المتوقع: ", DoubleToString(expectedBuy[i], 2));
      Print("مستوى البيع المحسوب: ", DoubleToString(calculatedSell, 2));
      Print("مستوى البيع المتوقع: ", DoubleToString(expectedSell[i], 2));
      Print("الفرق في الشراء: ", DoubleToString(MathAbs(calculatedBuy - expectedBuy[i]), 2));
      Print("الفرق في البيع: ", DoubleToString(MathAbs(calculatedSell - expectedSell[i]), 2));
      Print("");
   }
}
