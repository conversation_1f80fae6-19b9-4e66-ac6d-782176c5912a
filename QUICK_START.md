# دليل التشغيل السريع - إكسبرت Vagues EA

## 🚀 البدء السريع (5 دقائق)

### الخطوة 1: التحضير
```
✅ تأكد من تحميل مؤشر Vagues على الشارت
✅ تأكد من ظهور مستويات فيبوناتشي ملونة
✅ لاحظ الألوان المستخدمة (أحمر/أخضر)
```

### الخطوة 2: الاختبار السريع
```
1. ضع VaguesEA_QuickTest.mq4 على الشارت
2. راقب نافذة الخبراء للرسائل
3. تأكد من ظهور "✅ تم العثور على كائنات فيبوناتشي"
```

### الخطوة 3: التشغيل
```
1. أزل إكسبرت الاختبار
2. ضع VaguesEA.mq4 على الشارت
3. اضبط الإعدادات:
   - LotSize: 0.1 (أو حسب رغبتك)
   - RedFiboColor: clrRed
   - GreenFiboColor: clrLime
4. فعل التداول الآلي
```

## ⚙️ الإعدادات الأساسية

### إعدادات التداول
| الإعداد | القيمة المُوصاة | الوصف |
|---------|-----------------|-------|
| LotSize | 0.1 | حجم اللوت |
| StopLoss | 100 | وقف الخسارة (نقطة) |
| TakeProfit | 200 | جني الأرباح (نقطة) |

### إعدادات فيبوناتشي
| الإعداد | القيمة المُوصاة | الوصف |
|---------|-----------------|-------|
| FiboLevel | 0.618 | مستوى 61.8% |
| FiboTolerance | 5.0 | التسامح (نقطة) |
| RedFiboColor | clrRed | لون الشراء |
| GreenFiboColor | clrLime | لون البيع |

## 🎯 إشارات التداول

### إشارة الشراء
```
✅ مستوى فيبوناتشي أحمر
✅ السعر يلامس مستوى 61.8%
✅ ملامسة من الأسفل
→ فتح صفقة شراء
```

### إشارة البيع
```
✅ مستوى فيبوناتشي أخضر
✅ السعر يلامس مستوى 61.8%
✅ ملامسة من الأعلى
→ فتح صفقة بيع
```

## 📊 مراقبة الأداء

### معلومات الشارت
الإكسبرت يعرض:
- السعر الحالي
- عدد الصفقات المفتوحة
- إجمالي الربح
- نسبة النجاح
- أقرب مستويات فيبوناتشي

### نافذة الخبراء
راقب الرسائل:
- اكتشاف مستويات فيبوناتشي
- ملامسة المستويات
- فتح/إغلاق الصفقات
- الأخطاء والتحذيرات

## 🔧 استكشاف الأخطاء

### لا يفتح صفقات
```
❌ المشكلة: لا توجد صفقات
✅ الحل:
   1. تحقق من وجود مؤشر Vagues
   2. تأكد من ظهور مستويات فيبوناتشي
   3. راجع الألوان في الإعدادات
   4. استخدم إكسبرت الاختبار
```

### صفقات كثيرة
```
❌ المشكلة: صفقات متكررة
✅ الحل:
   1. زيد FiboTolerance إلى 10
   2. فعل OneTradePerSignal
   3. زيد minTimeBetweenTrades
```

### لا توجد مستويات فيبوناتشي
```
❌ المشكلة: لم يتم اكتشاف مستويات
✅ الحل:
   1. تأكد من تحميل مؤشر Vagues
   2. انتظر حتى يرسم المؤشر المستويات
   3. تحقق من إعدادات المؤشر
```

## 📱 التنبيهات

### تفعيل التنبيهات
```
EnableAlerts = true  // تنبيهات صوتية ومرئية
```

### أنواع التنبيهات
- إشارة شراء/بيع
- فتح صفقة جديدة
- إغلاق صفقة
- أخطاء التداول

## 🛡️ الأمان

### قبل التشغيل
```
⚠️ اختبر على حساب تجريبي
⚠️ ابدأ بحجم لوت صغير
⚠️ راقب الإكسبرت في البداية
⚠️ استخدم وقف خسارة مناسب
```

### أثناء التشغيل
```
👁️ راقب نافذة الخبراء
👁️ تابع الإحصائيات على الشارت
👁️ راجع الأداء بانتظام
👁️ اضبط الإعدادات حسب الحاجة
```

## 📞 الدعم السريع

### ملفات الدعم
- `VaguesEA_Instructions.txt` - دليل مفصل
- `VaguesEA_Test.mq4` - اختبار شامل
- `VaguesEA_QuickTest.mq4` - اختبار سريع
- `README.md` - معلومات عامة

### رسائل مهمة
```
✅ "تم العثور على كائنات فيبوناتشي" = يعمل بشكل صحيح
⚠️ "لم يتم العثور على مستويات" = تحقق من المؤشر
❌ "فشل في فتح صفقة" = تحقق من إعدادات التداول
```

## 🎉 نصائح للنجاح

1. **ابدأ بالاختبار** - استخدم الحساب التجريبي أولاً
2. **راقب الألوان** - تأكد من مطابقة ألوان فيبوناتشي
3. **اضبط التسامح** - ابدأ بـ 5 نقاط وعدل حسب الحاجة
4. **تابع الإحصائيات** - راقب نسبة النجاح والأرباح
5. **كن صبوراً** - انتظر الإشارات الصحيحة

---

**مبروك! إكسبرت Vagues EA جاهز للعمل** 🎊

*للدعم التقني، راجع الملفات المرفقة أو استخدم إكسبرت الاختبار للتشخيص.*
