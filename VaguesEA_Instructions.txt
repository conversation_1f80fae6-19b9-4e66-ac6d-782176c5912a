تعليمات استخدام إكسبرت Vagues EA
=====================================

نظرة عامة:
-----------
إكسبرت Vagues EA هو إكسبرت تداول آلي مصمم للعمل مع مؤشر Vagues. يقوم الإكسبرت بمراقبة مستويات فيبوناتشي الملونة على الشارت ويفتح الصفقات عند ملامسة السعر لمستوى 61.8%.

طريقة العمل:
-------------
الإكسبرت يعمل مع مؤشر Vagues الذي يرسم مستويات فيبوناتشي تلقائياً:

1. إشارة الشراء: عند ملامسة السعر لمستوى فيبوناتشي ذو اللون الأحمر عند مستوى 61.8%
2. إشارة البيع: عند ملامسة السعر لمستوى فيبوناتشي ذو اللون الأخضر عند مستوى 61.8%

ملاحظة مهمة: يجب أن يكون مؤشر Vagues مُحمّل على نفس الشارت قبل تشغيل الإكسبرت

الإعدادات الرئيسية:
-------------------

إعدادات التداول:
- LotSize: حجم اللوت (افتراضي: 0.1)
- MagicNumber: الرقم السحري للإكسبرت (افتراضي: 12345)
- Slippage: الانزلاق المسموح (افتراضي: 3)
- StopLoss: وقف الخسارة بالنقاط (افتراضي: 100)
- TakeProfit: جني الأرباح بالنقاط (افتراضي: 200)
- UseStopLoss: تفعيل وقف الخسارة (افتراضي: true)
- UseTakeProfit: تفعيل جني الأرباح (افتراضي: true)

إعدادات مؤشر Vagues:
- History: عدد الشموع للتحليل (افتراضي: 1000)
- Begin_draw_down: بداية الرسم للأسفل (افتراضي: 5)
- Begin_draw_up: بداية الرسم للأعلى (افتراضي: 5)
- Sektor_bar: شموع القطاع (افتراضي: 13)
- f_period_draw: فترة الرسم (افتراضي: 1)
- clear_opposite: مسح المقابل (افتراضي: 4)
- Wawe_bar: شموع الموجة (افتراضي: 21)
- f_draw_wawe_fibo: رسم فيبوناتشي الموجة (افتراضي: true)
- f_draw_target_level: رسم مستوى الهدف (افتراضي: true)
- DM_sdvig: إزاحة DM (افتراضي: 7)
- DM_xvost: ذيل DM (افتراضي: 10)
- f_draw_mini_trend: رسم الاتجاه المصغر (افتراضي: false)

إعدادات فيبوناتشي:
- FiboLevel: مستوى فيبوناتشي للدخول (افتراضي: 0.618 = 61.8%)
- FiboTolerance: التسامح في النقاط لملامسة المستوى (افتراضي: 5.0)
- RedFiboColor: لون فيبوناتشي الأحمر لإشارة الشراء (افتراضي: clrRed)
- GreenFiboColor: لون فيبوناتشي الأخضر لإشارة البيع (افتراضي: clrLime)
- CloseOppositeSignals: إغلاق الصفقات المعاكسة عند الإشارة الجديدة (افتراضي: true)
- OneTradePerSignal: صفقة واحدة فقط لكل إشارة (افتراضي: true)
- EnableAlerts: تفعيل التنبيهات الصوتية والمرئية (افتراضي: true)
- ShowDebugInfo: عرض معلومات التشخيص في نافذة الخبراء (افتراضي: true)

خطوات التشغيل:
---------------
1. ضع مؤشر Vagues على الشارت أولاً وتأكد من أنه يعرض مستويات فيبوناتشي
2. ضع الإكسبرت VaguesEA.mq4 على نفس الشارت
3. اضبط الإعدادات حسب احتياجاتك
4. تأكد من أن ألوان فيبوناتشي في الإعدادات تطابق الألوان المستخدمة في مؤشر Vagues
5. فعل التداول الآلي في MT4
6. راقب الإكسبرت وهو يعمل
7. تحقق من نافذة الخبراء (Experts) لرؤية الرسائل التشخيصية

ملاحظات مهمة:
- عند بدء تشغيل الإكسبرت، سيطبع قائمة بجميع الكائنات الموجودة على الشارت
- سيطبع تفاصيل مستويات فيبوناتشي المكتشفة كل 10 ثوان
- هذا يساعد في التأكد من أن الإكسبرت يكتشف مستويات فيبوناتشي بشكل صحيح

ميزات الإكسبرت:
---------------
- كشف تلقائي لمستويات فيبوناتشي الملونة
- فتح الصفقات عند ملامسة السعر لمستوى 61.8%
- إغلاق الصفقات المعاكسة عند الإشارة الجديدة (اختياري)
- تحكم في عدد الصفقات لكل إشارة
- عرض معلومات مفصلة على الشارت
- حساب المسافة إلى أقرب مستويات فيبوناتشي
- إدارة المخاطر مع وقف الخسارة وجني الأرباح
- تنبيهات صوتية ومرئية عند الإشارات والصفقات
- إحصائيات تداول شاملة (عدد الصفقات، نسبة النجاح، الأرباح)
- معلومات تشخيصية قابلة للتحكم
- تتبع أداء التداول في الوقت الفعلي

نصائح مهمة:
------------
1. تأكد من أن مؤشر Vagues يعمل بشكل صحيح ويعرض مستويات فيبوناتشي
2. اختبر الإكسبرت على حساب تجريبي أولاً
3. راقب الألوان المستخدمة في مستويات فيبوناتشي وتأكد من مطابقتها للإعدادات
4. استخدم إدارة مخاطر مناسبة
5. راجع الإعدادات بانتظام وعدلها حسب ظروف السوق

استكشاف الأخطاء:
-----------------
- إذا لم يفتح الإكسبرت صفقات، تحقق من:
  * وجود مؤشر Vagues على الشارت ويعمل بشكل صحيح
  * وجود مستويات فيبوناتشي مرسومة من المؤشر
  * مطابقة الألوان في الإعدادات مع ألوان المستويات في مؤشر Vagues
  * أن التداول الآلي مفعل في MT4
  * أن السعر يلامس فعلاً مستوى 61.8%
  * مراجعة نافذة الخبراء للرسائل التشخيصية

- إذا كانت الصفقات تفتح بكثرة، جرب:
  * زيادة قيمة FiboTolerance
  * تفعيل OneTradePerSignal
  * زيادة minTimeBetweenTrades

- للتشخيص:
  * راجع الرسائل في نافذة الخبراء عند بدء تشغيل الإكسبرت
  * ستجد قائمة بجميع الكائنات المكتشفة على الشارت
  * ستجد تفاصيل مستويات فيبوناتشي المكتشفة كل 10 ثوان
  * تأكد من أن أسماء كائنات فيبوناتشي تحتوي على "MyFibo" أو "Target" أو "Fibo"

معلومات الشارت:
----------------
يعرض الإكسبرت المعلومات التالية على الشارت:
- مستوى فيبوناتشي المستخدم
- التسامح المحدد
- ألوان فيبوناتشي
- السعر الحالي
- عدد الصفقات المفتوحة (شراء وبيع)
- إجمالي الربح
- عدد مستويات فيبوناتشي المكتشفة
- أقرب المستويات والمسافة إليها
- حالة الإشارات الحالية
- إحصائيات التداول الشاملة:
  * إجمالي صفقات الشراء والبيع
  * نسبة النجاح
  * إجمالي الأرباح والخسائر
  * عدد الصفقات الرابحة والخاسرة

الدعم:
-------
للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى التواصل مع المطور.

إخلاء المسؤولية:
------------------
هذا الإكسبرت مخصص للأغراض التعليمية والتجريبية. استخدمه على مسؤوليتك الخاصة وتأكد من اختباره جيداً قبل الاستخدام على حساب حقيقي.
