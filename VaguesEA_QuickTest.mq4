//+------------------------------------------------------------------+
//|                                         VaguesEA_QuickTest.mq4  |
//|                        اختبار سريع لإكسبرت Vagues EA            |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Alsayaf EA Quick Test"
#property link      ""
#property version   "1.00"
#property strict

//--- Input parameters
input bool TestMode = true;               // وضع الاختبار السريع

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("=== اختبار سريع لإكسبرت Vagues EA ===");
   
   if(TestMode)
   {
      Print("فحص توافق MQL4...");
      TestMQL4Compatibility();
      
      Print("فحص كائنات الشارت...");
      QuickObjectScan();
      
      Print("اختبار دوال فيبوناتشي...");
      TestFibonacciFunctions();
   }
   
   Print("=== انتهاء الاختبار السريع ===");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Print("تم إنهاء الاختبار السريع");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // لا حاجة لعمل شيء في التيك للاختبار السريع
}

//+------------------------------------------------------------------+
//| اختبار توافق MQL4                                               |
//+------------------------------------------------------------------+
void TestMQL4Compatibility()
{
   Print("- اختبار الدوال الأساسية:");
   Print("  * Ask: ", DoubleToString(Ask, Digits));
   Print("  * Bid: ", DoubleToString(Bid, Digits));
   Print("  * Point: ", DoubleToString(Point, Digits+1));
   Print("  * Digits: ", Digits);
   Print("  * Symbol: ", Symbol());
   
   Print("- اختبار الألوان:");
   color testRed = clrRed;
   color testGreen = clrLime;
   Print("  * اللون الأحمر: ", testRed);
   Print("  * اللون الأخضر: ", testGreen);
   
   Print("- اختبار أنواع كائنات فيبوناتشي المدعومة:");
   Print("  * OBJ_FIBO: ", OBJ_FIBO);
   Print("  * OBJ_FIBOFAN: ", OBJ_FIBOFAN);
   Print("  * OBJ_FIBOARC: ", OBJ_FIBOARC);
   Print("  * OBJ_FIBOCHANNEL: ", OBJ_FIBOCHANNEL);
   
   Print("✅ اختبار التوافق مكتمل");
}

//+------------------------------------------------------------------+
//| فحص سريع لكائنات الشارت                                        |
//+------------------------------------------------------------------+
void QuickObjectScan()
{
   int total = ObjectsTotal();
   Print("- إجمالي الكائنات على الشارت: ", total);
   
   if(total == 0)
   {
      Print("⚠️ تحذير: لا توجد كائنات على الشارت");
      Print("   تأكد من تحميل مؤشر Vagues أولاً");
      return;
   }
   
   int fiboObjects = 0;
   int redObjects = 0;
   int greenObjects = 0;
   
   for(int i = 0; i < total; i++)
   {
      string name = ObjectName(i);
      int objType = ObjectType(name);
      color objColor = (color)ObjectGet(name, OBJPROP_COLOR);
      
      // فحص كائنات فيبوناتشي
      bool isFibo = false;
      if(StringFind(name, "MyFibo") >= 0 || StringFind(name, "Target") >= 0 ||
         StringFind(name, "Fibo") >= 0 || StringFind(name, "fibo") >= 0 || 
         objType == OBJ_FIBO || objType == OBJ_FIBOFAN || 
         objType == OBJ_FIBOARC || objType == OBJ_FIBOCHANNEL)
      {
         isFibo = true;
         fiboObjects++;
         
         if(objColor == clrRed) redObjects++;
         if(objColor == clrLime) greenObjects++;
         
         Print("  كائن فيبوناتشي: ", name, " | اللون: ", objColor);
      }
   }
   
   Print("- ملخص الفحص:");
   Print("  * كائنات فيبوناتشي: ", fiboObjects);
   Print("  * كائنات حمراء: ", redObjects);
   Print("  * كائنات خضراء: ", greenObjects);
   
   if(fiboObjects == 0)
   {
      Print("⚠️ تحذير: لم يتم العثور على كائنات فيبوناتشي");
      Print("   تأكد من أن مؤشر Vagues يرسم مستويات فيبوناتشي");
   }
   else
   {
      Print("✅ تم العثور على كائنات فيبوناتشي");
   }
}

//+------------------------------------------------------------------+
//| اختبار دوال فيبوناتشي                                          |
//+------------------------------------------------------------------+
void TestFibonacciFunctions()
{
   Print("- اختبار حساب مستوى فيبوناتشي 61.8%:");
   
   double price1 = 1.2000;
   double price2 = 1.2100;
   double level618 = price1 + (price2 - price1) * 0.618;
   
   Print("  * السعر الأول: ", DoubleToString(price1, 5));
   Print("  * السعر الثاني: ", DoubleToString(price2, 5));
   Print("  * مستوى 61.8%: ", DoubleToString(level618, 5));
   
   Print("- اختبار حساب المسافة:");
   double currentPrice = (Ask + Bid) / 2.0;
   double distance = MathAbs(currentPrice - level618) / Point;
   Print("  * السعر الحالي: ", DoubleToString(currentPrice, Digits));
   Print("  * المسافة بالنقاط: ", DoubleToString(distance, 1));
   
   Print("- اختبار شروط الملامسة:");
   double tolerance = 5.0 * Point;
   bool isNear = (MathAbs(currentPrice - level618) <= tolerance);
   Print("  * التسامح: ", DoubleToString(tolerance, Digits+1));
   Print("  * قريب من المستوى: ", (isNear ? "نعم" : "لا"));
   
   Print("✅ اختبار دوال فيبوناتشي مكتمل");
}

//+------------------------------------------------------------------+
//| اختبار إنشاء كائن فيبوناتشي تجريبي                             |
//+------------------------------------------------------------------+
void CreateTestFibonacci()
{
   string objName = "TestFibo_" + IntegerToString(TimeCurrent());
   
   if(ObjectCreate(objName, OBJ_FIBO, 0, Time[10], High[10], Time[5], Low[5]))
   {
      ObjectSet(objName, OBJPROP_COLOR, clrRed);
      ObjectSet(objName, OBJPROP_WIDTH, 2);
      Print("✅ تم إنشاء كائن فيبوناتشي تجريبي: ", objName);
   }
   else
   {
      Print("❌ فشل في إنشاء كائن فيبوناتشي تجريبي");
   }
}
