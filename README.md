# إكسبرت Vagues EA

إكسبرت تداول آلي مصمم للعمل مع مؤشر Vagues لفتح الصفقات عند ملامسة السعر لمستويات فيبوناتشي.

## الملفات المتضمنة

- **VaguesEA.mq4** - الإكسبرت الرئيسي
- **VaguesEA_Test.mq4** - إكسبرت اختبار للتشخيص
- **VaguesEA.set** - ملف الإعدادات الجاهز
- **VaguesEA_Instructions.txt** - دليل الاستخدام التفصيلي

## طريقة العمل

### إشارات التداول
- **إشارة الشراء**: عند ملامسة السعر لمستوى فيبوناتشي أحمر عند 61.8%
- **إشارة البيع**: عند ملامسة السعر لمستوى فيبوناتشي أخضر عند 61.8%

### المتطلبات
1. مؤشر Vagues يجب أن يكون مُحمّل على الشارت
2. المؤشر يجب أن يرسم مستويات فيبوناتشي بألوان مختلفة
3. MT4 مع تفعيل التداول الآلي

## خطوات التشغيل

### 1. التحضير
```
1. ضع مؤشر Vagues على الشارت
2. تأكد من ظهور مستويات فيبوناتشي ملونة
3. لاحظ الألوان المستخدمة (أحمر/أخضر)
```

### 2. الاختبار (مُوصى به)
```
1. ضع VaguesEA_Test.mq4 على الشارت أولاً
2. راقب نافذة الخبراء للرسائل التشخيصية
3. تأكد من اكتشاف مستويات فيبوناتشي بشكل صحيح
```

### 3. التشغيل الفعلي
```
1. أزل إكسبرت الاختبار
2. ضع VaguesEA.mq4 على الشارت
3. اضبط الإعدادات (الألوان، حجم اللوت، إلخ)
4. فعل التداول الآلي
```

## الإعدادات الرئيسية

### إعدادات التداول
- `LotSize`: حجم اللوت (0.1)
- `StopLoss`: وقف الخسارة بالنقاط (100)
- `TakeProfit`: جني الأرباح بالنقاط (200)

### إعدادات فيبوناتشي
- `FiboLevel`: مستوى فيبوناتشي (0.618 = 61.8%)
- `FiboTolerance`: التسامح بالنقاط (5.0)
- `RedFiboColor`: لون الشراء (clrRed)
- `GreenFiboColor`: لون البيع (clrLime)

### إعدادات متقدمة
- `CloseOppositeSignals`: إغلاق الصفقات المعاكسة (true)
- `OneTradePerSignal`: صفقة واحدة لكل إشارة (true)

## الميزات

✅ **كشف تلقائي** لمستويات فيبوناتشي من مؤشر Vagues  
✅ **تشخيص شامل** مع رسائل تفصيلية  
✅ **إدارة المخاطر** مع وقف الخسارة وجني الأرباح  
✅ **إغلاق الصفقات المعاكسة** عند الإشارة الجديدة  
✅ **تحكم في عدد الصفقات** لكل إشارة  
✅ **معلومات مفصلة** على الشارت  

## استكشاف الأخطاء

### لا يفتح صفقات
1. تحقق من وجود مؤشر Vagues على الشارت
2. تأكد من رسم مستويات فيبوناتشي
3. راجع الألوان في الإعدادات
4. استخدم إكسبرت الاختبار للتشخيص

### صفقات كثيرة
1. زيد قيمة `FiboTolerance`
2. فعل `OneTradePerSignal`
3. زيد `minTimeBetweenTrades`

### رسائل التشخيص
- راقب نافذة الخبراء عند بدء التشغيل
- ستجد قائمة بجميع الكائنات المكتشفة
- تفاصيل مستويات فيبوناتشي كل 10 ثوان

## أسماء الكائنات المدعومة

الإكسبرت يبحث عن الكائنات التالية:
- `MyFibo_sell`, `MyFibo_buy`
- `Target_up`, `Target_down`
- أي كائن يحتوي على `Fibo` أو `fibo`
- كائنات فيبوناتشي القياسية في MT4

## الأمان

⚠️ **تحذيرات مهمة**:
- اختبر على حساب تجريبي أولاً
- راجع الإعدادات بعناية
- راقب الإكسبرت أثناء العمل
- استخدم إدارة مخاطر مناسبة

## الدعم

للحصول على الدعم:
1. راجع ملف التعليمات التفصيلي
2. استخدم إكسبرت الاختبار للتشخيص
3. راقب الرسائل في نافذة الخبراء

## إخلاء المسؤولية

هذا الإكسبرت مخصص للأغراض التعليمية والتجريبية. استخدمه على مسؤوليتك الخاصة وتأكد من اختباره جيداً قبل الاستخدام على حساب حقيقي.
